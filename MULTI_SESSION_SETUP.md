# Multi-Session Device Testing Setup Guide

This guide explains how to run multiple instances of the Mobile App Automation Tool simultaneously with different devices for parallel testing.

## 🎯 Overview

The application supports running multiple sessions in parallel, each with:
- **Separate Flask web interface** (different ports)
- **Dedicated Appium server** (different ports)
- **Isolated WebDriverAgent** (different ports for iOS)
- **Independent device connections** (no interference between sessions)

## 🚀 Quick Start

### Method 1: Automated Multi-Session Startup

Use the provided script to start multiple sessions automatically:

```bash
# Start 2 sessions with default port increments
./start_multi_device.sh

# Start 3 sessions
./start_multi_device.sh --instances 3

# Start with custom base ports
./start_multi_device.sh --base-port 9000 --base-appium 5000 --base-wda 9100
```

### Method 2: Manual Session Startup

Open separate terminals and start each session manually:

```bash
# Terminal 1 - Session 1 (Default ports)
source venv/bin/activate
python run.py

# Terminal 2 - Session 2
source venv/bin/activate
python run.py --port 8081 --appium-port 4724 --wda-port 8101

# Terminal 3 - Session 3
source venv/bin/activate
python run.py --port 8082 --appium-port 4725 --wda-port 8102
```

## 📋 Port Configuration

### Default Port Assignments

| Session | Flask Port | Appium Port | WDA Port | Web Interface |
|---------|------------|-------------|----------|---------------|
| 1       | 8080       | 4723        | 8100     | http://localhost:8080 |
| 2       | 8081       | 4724        | 8101     | http://localhost:8081 |
| 3       | 8082       | 4725        | 8102     | http://localhost:8082 |
| 4       | 8083       | 4726        | 8103     | http://localhost:8083 |

### Command Line Arguments

| Argument | Description | Default |
|----------|-------------|---------|
| `--port` | Flask web server port | 8080 |
| `--appium-port` | Appium server port | 4723 |
| `--wda-port` | WebDriverAgent port (iOS only) | 8100 |

## 📱 Device Connection Workflow

### Step 1: Start Multiple Sessions

```bash
# Example: Start 3 sessions for 3 devices
./start_multi_device.sh --instances 3
```

### Step 2: Access Web Interfaces

Open your browser and navigate to each session:
- **Session 1**: http://localhost:8080
- **Session 2**: http://localhost:8081  
- **Session 3**: http://localhost:8082

### Step 3: Connect Different Devices

In each web interface:

1. **Go to Device Control tab**
2. **Select different device** from the dropdown
3. **Click Connect**
4. **Verify device screen** shows the correct device

### Step 4: Run Tests in Parallel

Each session can now:
- Create and run test cases independently
- Execute test suites simultaneously
- Generate separate reports
- Perform parallel automation

## 🍎 iOS Device Setup

### Automatic WebDriverAgent Management

The application automatically manages WebDriverAgent for iOS devices:

```bash
# WebDriverAgent ports are assigned automatically:
# Device 1 → Port 8100
# Device 2 → Port 8101  
# Device 3 → Port 8102
```

### Manual WebDriverAgent Setup (if needed)

If automatic setup fails, you can manually start WebDriverAgent:

```bash
# For device on port 8101
export WDA_PORT=8101
./restart_wda.sh

# Or use the WDA runner directly
python WDA_agent_runner.py <DEVICE_UDID> <TEAM_ID> --wda-port 8101
```

## 🤖 Android Device Setup

Android devices work automatically with ADB. Ensure:

1. **USB Debugging** is enabled
2. **Devices are authorized** for debugging
3. **Different devices** are connected to different sessions

## 🔧 Troubleshooting

### Port Conflicts

Check what's using specific ports:
```bash
lsof -i :8080  # Flask port
lsof -i :4723  # Appium port  
lsof -i :8100  # WebDriverAgent port
```

Kill processes on specific ports:
```bash
kill -9 $(lsof -ti:8080)
kill -9 $(lsof -ti:4723)
kill -9 $(lsof -ti:8100)
```

### Device Not Showing Correct Screen

If multiple sessions show the same device:

1. **Restart both sessions**:
   ```bash
   pkill -f "python run.py"
   # Wait 5 seconds
   ./start_multi_device.sh --instances 2
   ```

2. **Check device connections**:
   ```bash
   # iOS devices
   tidevice list
   
   # Android devices  
   adb devices
   ```

3. **Verify port isolation**:
   ```bash
   # Check WebDriverAgent processes
   ps aux | grep tidevice
   ps aux | grep iproxy
   ```

### Session Database Conflicts

Each session uses isolated databases automatically. If you encounter issues:

```bash
# Clear session-specific data
rm -f *.db_port_*
rm -f *_port_*.db
```

## 📊 Example Multi-Device Testing Scenario

### Scenario: Test App on 3 Different Devices

1. **Start 3 sessions**:
   ```bash
   ./start_multi_device.sh --instances 3
   ```

2. **Connect devices**:
   - Session 1 (8080): iPhone 12 Pro
   - Session 2 (8081): iPhone 15 Pro Max  
   - Session 3 (8082): Samsung Galaxy S23

3. **Run parallel tests**:
   - Each session runs the same test suite
   - Different device behaviors captured
   - Separate reports generated

4. **Compare results**:
   - Review reports from each session
   - Identify device-specific issues
   - Optimize for different screen sizes

## 🎯 Best Practices

### Resource Management
- **Limit concurrent sessions** based on system resources
- **Monitor CPU and memory** usage during parallel testing
- **Close unused sessions** to free up ports

### Test Organization
- **Use descriptive test names** including device info
- **Organize reports** by device type/session
- **Document device-specific behaviors**

### Network Considerations
- **Ensure stable USB connections** for physical devices
- **Avoid network-intensive operations** during parallel testing
- **Monitor device temperatures** during extended testing

## 🔍 Verification Commands

Check if multi-session setup is working correctly:

```bash
# Check running Flask instances
ps aux | grep "python run.py"

# Check Appium servers
ps aux | grep appium

# Check WebDriverAgent processes (iOS)
ps aux | grep tidevice
ps aux | grep iproxy

# Check port usage
netstat -an | grep LISTEN | grep -E "(8080|8081|8082|4723|4724|4725|8100|8101|8102)"
```

## 📞 Support

If you encounter issues:

1. **Check the logs** in each terminal session
2. **Verify device connections** using platform tools
3. **Ensure port availability** before starting sessions
4. **Restart sessions** if device screens are shared

---

**🎉 Happy Multi-Device Testing!**

This setup enables efficient parallel testing across multiple devices, significantly reducing test execution time and improving coverage.
