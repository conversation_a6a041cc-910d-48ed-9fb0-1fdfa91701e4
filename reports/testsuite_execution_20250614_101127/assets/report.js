// Report interaction JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize expand/collapse functionality for test suites
    const suiteHeadings = document.querySelectorAll('.suite-heading');
    suiteHeadings.forEach(heading => {
        heading.addEventListener('click', function() {
            const parentElement = this.parentElement;
            parentElement.classList.toggle('expanded');
            const testList = parentElement.querySelector('.test-list');
            if (testList) {
                testList.style.display = parentElement.classList.contains('expanded') ? 'block' : 'none';
            }
        });
    });

    // Initialize expand/collapse functionality for test cases
    const testHeaders = document.querySelectorAll('.test-header');
    testHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const testItem = this.parentElement;
            testItem.classList.toggle('expanded');
            const steps = testItem.querySelector('.test-steps');
            if (steps) {
                steps.style.display = testItem.classList.contains('expanded') ? 'block' : 'none';
            }
        });
    });

    // Expand all suites by default
    suiteHeadings.forEach(heading => {
        const parentElement = heading.parentElement;
        parentElement.classList.add('expanded');
        const testList = parentElement.querySelector('.test-list');
        if (testList) {
            testList.style.display = 'block';
        }
    });

    // Simple function to handle missing screenshots
    window.handleMissingScreenshot = function(imgElement) {
        // Basic fallback if fetching directory is not supported
        imgElement.style.display = 'none';
        const noScreenshotMsg = imgElement.nextElementSibling;
        if (noScreenshotMsg) {
            noScreenshotMsg.style.display = 'block';
            noScreenshotMsg.style.color = 'rgb(108, 117, 125)';
        }
    };

    // Function to show step details
    window.showStepDetails = function(stepId) {
        const detailsPanel = document.getElementById('details-panel');
        if (!detailsPanel) return;

        const [_, testCaseIndex, stepIndex] = stepId.split('-');

        // Get the test data from the script that has it
        const testDataScript = document.querySelector('script:not([src])');
        if (!testDataScript) {
            detailsPanel.innerHTML = '<h3>Error: Test data not found</h3>';
            return;
        }

        try {
            // Extract the test data from the script content
            const scriptContent = testDataScript.textContent;
            const testDataMatch = scriptContent.match(/const testData = (.*?);/s);
            if (!testDataMatch) {
                detailsPanel.innerHTML = '<h3>Error: Test data format not recognized</h3>';
                return;
            }

            const testDataString = testDataMatch[1];
            const testData = JSON.parse(testDataString);

            const testCase = testData.testCases[testCaseIndex];
            const step = testCase.steps[stepIndex];

            // Create content
            // Escape HTML special characters in step name and test case name
            const escapedStepName = (step.name || 'Unnamed Step')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');

            const escapedTestCaseName = (testCase.name || 'Unnamed Test Case')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');

            let detailsContent = '<div class="step-details">' +
                '<h3>' + escapedStepName + '</h3>' +
                '<p><strong>Status:</strong> <span class="status-badge status-badge-' + step.status + '">' + step.status + '</span></p>' +
                '<p><strong>Duration:</strong> ' + step.duration + '</p>' +
                '<p><strong>Test Case:</strong> ' + escapedTestCaseName + '</p>';

            // All steps in the report should be Add Log steps now
            // But double-check just to be safe
            const isAddLogStep = step.is_add_log === true;

            // Get screenshot path from the resolved_screenshot property
            let imgSrc = '';

            // First check if we have a direct screenshot from the step
            if (step.resolved_screenshot) {
                imgSrc = step.resolved_screenshot;
                console.log('Using screenshot from step: ' + imgSrc);
            }
            // If no screenshot was found, check for any alternative
            else if (step.alt_screenshot) {
                imgSrc = step.alt_screenshot;
                console.log('Using alternative screenshot: ' + imgSrc);
            }
            // Default to a blank source if nothing else is available
            else {
                imgSrc = '';
                console.log('No screenshot available for this step');
            }

            // Add data-step-id attribute to help with debugging
            // Get the action_id if available
            const actionId = step.action_id || '';

            // Add action_id display if available
            let actionIdHtml = '';
            if (actionId) {
                actionIdHtml = '<div class="action-id">Action ID: ' + actionId + '</div>';
            }

            detailsContent += '<div class="screenshot-container">' +
                '<h4>Screenshot:</h4>' +
                actionIdHtml +
                '<img src="' + imgSrc + '" alt="Step Screenshot" data-step-id="' + stepIndex + '" ' +
                'onerror="this.onerror=null; this.src=\'screenshots/' + actionId + '.png\'; ' +
                'this.onerror=function(){this.onerror=null; handleMissingScreenshot(this);}" />' +
                '<p id="no-screenshot-' + stepIndex + '" style="display: none;">No screenshot available for this step</p>' +
                '</div>';

            // Add error info if present
            if (step.error) {
                // Escape HTML special characters in error message
                const escapedError = (step.error || '')
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#039;');

                detailsContent += '<div class="error-details">' +
                    '<h4>Error:</h4>' +
                    '<pre>' + escapedError + '</pre>' +
                    '</div>';
            }

            detailsContent += '</div>';

            detailsPanel.innerHTML = detailsContent;
        } catch (error) {
            detailsPanel.innerHTML = '<h3>Error processing test data: ' + error.message + '</h3>';
        }
    };

    // Highlight active step when clicked
    const testSteps = document.querySelectorAll('.test-step');
    testSteps.forEach(step => {
        step.addEventListener('click', function() {
            // Remove highlight from all steps
            testSteps.forEach(s => s.classList.remove('active'));
            // Add highlight to current step
            this.classList.add('active');
        });
    });
});