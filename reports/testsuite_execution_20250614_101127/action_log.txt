Action Log - 2025-06-14 10:14:38
================================================================================

[[10:14:37]] [INFO] Generating execution report...
[[10:14:37]] [SUCCESS] Screenshot refreshed successfully
[[10:14:37]] [SUCCESS] Screenshot refreshed
[[10:14:36]] [INFO] Refreshing screenshot...
[[10:14:34]] [SUCCESS] Screenshot refreshed successfully
[[10:14:34]] [INFO] Executing action 25/25: Add Log: App is closed (with screenshot)
[[10:14:34]] [SUCCESS] Screenshot refreshed
[[10:14:32]] [INFO] Refreshing screenshot...
[[10:14:32]] [SUCCESS] Screenshot refreshed successfully
[[10:14:32]] [SUCCESS] Screenshot refreshed
[[10:14:31]] [INFO] Refreshing screenshot...
[[10:14:29]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[10:14:28]] [SUCCESS] Screenshot refreshed successfully
[[10:14:28]] [SUCCESS] Screenshot refreshed
[[10:14:27]] [INFO] Refreshing screenshot...
[[10:14:13]] [INFO] Executing Multi Step action step 8/9: Execute Test Case: apple health (8 steps)
[[10:14:13]] [SUCCESS] Screenshot refreshed successfully
[[10:14:13]] [SUCCESS] Screenshot refreshed
[[10:14:12]] [INFO] Refreshing screenshot...
[[10:14:09]] [INFO] Executing Multi Step action step 7/9: Terminate app: com.apple.Health
[[10:14:08]] [SUCCESS] Screenshot refreshed successfully
[[10:14:08]] [SUCCESS] Screenshot refreshed
[[10:14:07]] [INFO] Refreshing screenshot...
[[10:14:04]] [INFO] Executing Multi Step action step 6/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:14:04]] [SUCCESS] Screenshot refreshed successfully
[[10:14:04]] [SUCCESS] Screenshot refreshed
[[10:14:03]] [INFO] Refreshing screenshot...
[[10:14:02]] [INFO] Executing Multi Step action step 5/9: Add Log: Clicked on Edit link successfully (with screenshot)
[[10:14:01]] [SUCCESS] Screenshot refreshed successfully
[[10:14:01]] [SUCCESS] Screenshot refreshed
[[10:14:01]] [INFO] Refreshing screenshot...
[[10:13:58]] [INFO] Executing Multi Step action step 4/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[10:13:58]] [SUCCESS] Screenshot refreshed successfully
[[10:13:58]] [SUCCESS] Screenshot refreshed
[[10:13:57]] [INFO] Refreshing screenshot...
[[10:13:54]] [INFO] Executing Multi Step action step 3/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:13:54]] [SUCCESS] Screenshot refreshed successfully
[[10:13:54]] [SUCCESS] Screenshot refreshed
[[10:13:53]] [INFO] Refreshing screenshot...
[[10:13:51]] [INFO] Executing Multi Step action step 2/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[10:13:50]] [SUCCESS] Screenshot refreshed successfully
[[10:13:50]] [SUCCESS] Screenshot refreshed
[[10:13:50]] [INFO] Refreshing screenshot...
[[10:13:45]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[10:13:45]] [INFO] Loaded 9 steps from test case: apple health
[[10:13:45]] [INFO] Loading steps for Multi Step action: apple health
[[10:13:45]] [INFO] Executing action 24/25: Execute Test Case: apple health (8 steps)
[[10:13:44]] [SUCCESS] Screenshot refreshed successfully
[[10:13:44]] [SUCCESS] Screenshot refreshed
[[10:13:43]] [INFO] Refreshing screenshot...
[[10:13:40]] [INFO] Executing action 23/25: Terminate app: com.apple.Health
[[10:13:40]] [SUCCESS] Screenshot refreshed successfully
[[10:13:40]] [SUCCESS] Screenshot refreshed
[[10:13:39]] [INFO] Refreshing screenshot...
[[10:13:36]] [INFO] Executing action 22/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:13:36]] [SUCCESS] Screenshot refreshed successfully
[[10:13:36]] [SUCCESS] Screenshot refreshed
[[10:13:35]] [INFO] Refreshing screenshot...
[[10:13:34]] [INFO] Executing action 21/25: Add Log: Clicked on Edit link successfully (with screenshot)
[[10:13:33]] [SUCCESS] Screenshot refreshed successfully
[[10:13:33]] [SUCCESS] Screenshot refreshed
[[10:13:33]] [INFO] Refreshing screenshot...
[[10:13:30]] [INFO] Executing action 20/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[10:13:29]] [SUCCESS] Screenshot refreshed successfully
[[10:13:29]] [SUCCESS] Screenshot refreshed
[[10:13:29]] [INFO] Refreshing screenshot...
[[10:13:26]] [INFO] Executing action 19/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:13:25]] [SUCCESS] Screenshot refreshed successfully
[[10:13:25]] [SUCCESS] Screenshot refreshed
[[10:13:25]] [INFO] Refreshing screenshot...
[[10:13:22]] [INFO] Executing action 18/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[10:13:22]] [SUCCESS] Screenshot refreshed successfully
[[10:13:22]] [SUCCESS] Screenshot refreshed
[[10:13:21]] [INFO] Refreshing screenshot...
[[10:13:07]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[10:13:06]] [SUCCESS] Screenshot refreshed successfully
[[10:13:06]] [SUCCESS] Screenshot refreshed
[[10:13:05]] [SUCCESS] Screenshot refreshed successfully
[[10:13:05]] [INFO] Refreshing screenshot...
[[10:13:05]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[10:13:05]] [INFO] Executing action 16/25: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[10:13:05]] [SUCCESS] Screenshot refreshed
[[10:13:04]] [INFO] Refreshing screenshot...
[[10:13:01]] [SUCCESS] Screenshot refreshed successfully
[[10:13:01]] [SUCCESS] Screenshot refreshed
[[10:13:00]] [INFO] Refreshing screenshot...
[[10:12:57]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[10:12:57]] [SUCCESS] Screenshot refreshed successfully
[[10:12:57]] [SUCCESS] Screenshot refreshed
[[10:12:56]] [INFO] Refreshing screenshot...
[[10:12:53]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[10:12:52]] [SUCCESS] Screenshot refreshed successfully
[[10:12:52]] [SUCCESS] Screenshot refreshed
[[10:12:52]] [INFO] Refreshing screenshot...
[[10:12:49]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[10:12:48]] [SUCCESS] Screenshot refreshed successfully
[[10:12:48]] [SUCCESS] Screenshot refreshed
[[10:12:48]] [INFO] Refreshing screenshot...
[[10:12:46]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[10:12:46]] [SUCCESS] Screenshot refreshed successfully
[[10:12:46]] [SUCCESS] Screenshot refreshed
[[10:12:45]] [INFO] Refreshing screenshot...
[[10:12:42]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:12:42]] [SUCCESS] Screenshot refreshed successfully
[[10:12:42]] [SUCCESS] Screenshot refreshed
[[10:12:41]] [INFO] Refreshing screenshot...
[[10:12:40]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[10:12:39]] [SUCCESS] Screenshot refreshed successfully
[[10:12:39]] [SUCCESS] Screenshot refreshed
[[10:12:39]] [INFO] Refreshing screenshot...
[[10:12:36]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[10:12:36]] [SUCCESS] Screenshot refreshed successfully
[[10:12:36]] [SUCCESS] Screenshot refreshed
[[10:12:35]] [INFO] Refreshing screenshot...
[[10:12:34]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[10:12:33]] [SUCCESS] Screenshot refreshed successfully
[[10:12:33]] [SUCCESS] Screenshot refreshed
[[10:12:33]] [INFO] Refreshing screenshot...
[[10:12:28]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[10:12:28]] [INFO] Loaded 9 steps from test case: health2
[[10:12:28]] [INFO] Loading steps for Multi Step action: health2
[[10:12:28]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[10:12:27]] [SUCCESS] Screenshot refreshed successfully
[[10:12:27]] [SUCCESS] Screenshot refreshed
[[10:12:26]] [INFO] Refreshing screenshot...
[[10:12:24]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[10:12:24]] [SUCCESS] Screenshot refreshed successfully
[[10:12:24]] [SUCCESS] Screenshot refreshed
[[10:12:23]] [INFO] Refreshing screenshot...
[[10:12:19]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[10:12:19]] [SUCCESS] Screenshot refreshed successfully
[[10:12:19]] [SUCCESS] Screenshot refreshed
[[10:12:18]] [INFO] Refreshing screenshot...
[[10:12:15]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:12:15]] [SUCCESS] Screenshot refreshed successfully
[[10:12:15]] [SUCCESS] Screenshot refreshed
[[10:12:14]] [INFO] Refreshing screenshot...
[[10:12:12]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[10:12:11]] [SUCCESS] Screenshot refreshed successfully
[[10:12:11]] [SUCCESS] Screenshot refreshed
[[10:12:11]] [INFO] Refreshing screenshot...
[[10:11:58]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[10:11:58]] [SUCCESS] Screenshot refreshed successfully
[[10:11:58]] [SUCCESS] Screenshot refreshed
[[10:11:57]] [INFO] Refreshing screenshot...
[[10:11:54]] [INFO] Executing action 9/25: Add Log: App is closed (with screenshot)
[[10:11:54]] [SUCCESS] Screenshot refreshed successfully
[[10:11:54]] [SUCCESS] Screenshot refreshed
[[10:11:53]] [INFO] Refreshing screenshot...
[[10:11:50]] [INFO] Executing action 8/25: Terminate app: com.apple.Health
[[10:11:49]] [SUCCESS] Screenshot refreshed successfully
[[10:11:49]] [SUCCESS] Screenshot refreshed
[[10:11:49]] [INFO] Refreshing screenshot...
[[10:11:46]] [INFO] Executing action 7/25: Wait for 1 ms
[[10:11:46]] [SUCCESS] Screenshot refreshed successfully
[[10:11:45]] [SUCCESS] Screenshot refreshed
[[10:11:45]] [INFO] Refreshing screenshot...
[[10:11:43]] [INFO] Executing action 6/25: Add Log: Done link is clicked (with screenshot)
[[10:11:43]] [SUCCESS] Screenshot refreshed successfully
[[10:11:43]] [SUCCESS] Screenshot refreshed
[[10:11:42]] [INFO] Refreshing screenshot...
[[10:11:39]] [INFO] Executing action 5/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[10:11:39]] [SUCCESS] Screenshot refreshed successfully
[[10:11:39]] [SUCCESS] Screenshot refreshed
[[10:11:38]] [INFO] Refreshing screenshot...
[[10:11:37]] [INFO] Executing action 4/25: Add Log: Edit link is clicked (with screenshot)
[[10:11:36]] [SUCCESS] Screenshot refreshed successfully
[[10:11:36]] [SUCCESS] Screenshot refreshed
[[10:11:36]] [INFO] Refreshing screenshot...
[[10:11:33]] [INFO] Executing action 3/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[10:11:33]] [SUCCESS] Screenshot refreshed successfully
[[10:11:33]] [SUCCESS] Screenshot refreshed
[[10:11:32]] [INFO] Refreshing screenshot...
[[10:11:31]] [INFO] Executing action 2/25: Add Log: Launched App Successfully (with screenshot)
[[10:11:30]] [SUCCESS] Screenshot refreshed successfully
[[10:11:30]] [SUCCESS] Screenshot refreshed
[[10:11:29]] [INFO] Refreshing screenshot...
[[10:11:27]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[10:11:27]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[10:11:27]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[10:11:27]] [INFO] Clearing screenshots from database before execution...
[[10:11:27]] [SUCCESS] All screenshots deleted successfully
[[10:11:27]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[10:11:27]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_101127/screenshots
[[10:11:27]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_101127
[[10:11:27]] [SUCCESS] Report directory initialized successfully
[[10:11:27]] [INFO] Initializing report directory and screenshots folder...
[[10:11:23]] [SUCCESS] All screenshots deleted successfully
[[10:11:23]] [INFO] All actions cleared
[[10:11:23]] [INFO] Cleaning up screenshots...
[[10:11:18]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[10:11:18]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[10:11:15]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[10:11:02]] [SUCCESS] Found 1 device(s)
[[10:11:01]] [INFO] Refreshing device list...
