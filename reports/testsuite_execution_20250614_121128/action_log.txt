Action Log - 2025-06-14 12:14:36
================================================================================

[[12:14:35]] [INFO] Generating execution report...
[[12:14:35]] [SUCCESS] Screenshot refreshed successfully
[[12:14:35]] [SUCCESS] Screenshot refreshed
[[12:14:34]] [INFO] Refreshing screenshot...
[[12:14:32]] [SUCCESS] Screenshot refreshed successfully
[[12:14:32]] [INFO] Executing action 25/25: Add Log: App is closed (with screenshot)
[[12:14:32]] [SUCCESS] Screenshot refreshed
[[12:14:31]] [INFO] Refreshing screenshot...
[[12:14:30]] [SUCCESS] Screenshot refreshed successfully
[[12:14:30]] [SUCCESS] Screenshot refreshed
[[12:14:29]] [INFO] Refreshing screenshot...
[[12:14:27]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[12:14:26]] [SUCCESS] Screenshot refreshed successfully
[[12:14:26]] [SUCCESS] Screenshot refreshed
[[12:14:25]] [INFO] Refreshing screenshot...
[[12:14:11]] [INFO] Executing Multi Step action step 8/9: Execute Test Case: apple health (8 steps)
[[12:14:11]] [SUCCESS] Screenshot refreshed successfully
[[12:14:11]] [SUCCESS] Screenshot refreshed
[[12:14:10]] [INFO] Refreshing screenshot...
[[12:14:06]] [INFO] Executing Multi Step action step 7/9: Terminate app: com.apple.Health
[[12:14:06]] [SUCCESS] Screenshot refreshed successfully
[[12:14:06]] [SUCCESS] Screenshot refreshed
[[12:14:05]] [INFO] Refreshing screenshot...
[[12:14:02]] [INFO] Executing Multi Step action step 6/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:14:02]] [SUCCESS] Screenshot refreshed successfully
[[12:14:02]] [SUCCESS] Screenshot refreshed
[[12:14:01]] [INFO] Refreshing screenshot...
[[12:14:00]] [INFO] Executing Multi Step action step 5/9: Add Log: Clicked on Edit link successfully (with screenshot)
[[12:13:59]] [SUCCESS] Screenshot refreshed successfully
[[12:13:59]] [SUCCESS] Screenshot refreshed
[[12:13:59]] [INFO] Refreshing screenshot...
[[12:13:56]] [INFO] Executing Multi Step action step 4/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:13:55]] [SUCCESS] Screenshot refreshed successfully
[[12:13:55]] [SUCCESS] Screenshot refreshed
[[12:13:55]] [INFO] Refreshing screenshot...
[[12:13:52]] [INFO] Executing Multi Step action step 3/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:13:51]] [SUCCESS] Screenshot refreshed successfully
[[12:13:51]] [SUCCESS] Screenshot refreshed
[[12:13:51]] [INFO] Refreshing screenshot...
[[12:13:48]] [INFO] Executing Multi Step action step 2/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:13:48]] [SUCCESS] Screenshot refreshed successfully
[[12:13:48]] [SUCCESS] Screenshot refreshed
[[12:13:47]] [INFO] Refreshing screenshot...
[[12:13:42]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[12:13:42]] [INFO] Loaded 9 steps from test case: apple health
[[12:13:42]] [INFO] Loading steps for Multi Step action: apple health
[[12:13:42]] [INFO] Executing action 24/25: Execute Test Case: apple health (8 steps)
[[12:13:42]] [SUCCESS] Screenshot refreshed successfully
[[12:13:42]] [SUCCESS] Screenshot refreshed
[[12:13:41]] [INFO] Refreshing screenshot...
[[12:13:37]] [INFO] Executing action 23/25: Terminate app: com.apple.Health
[[12:13:37]] [SUCCESS] Screenshot refreshed successfully
[[12:13:37]] [SUCCESS] Screenshot refreshed
[[12:13:36]] [INFO] Refreshing screenshot...
[[12:13:33]] [INFO] Executing action 22/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:13:33]] [SUCCESS] Screenshot refreshed successfully
[[12:13:33]] [SUCCESS] Screenshot refreshed
[[12:13:32]] [INFO] Refreshing screenshot...
[[12:13:31]] [INFO] Executing action 21/25: Add Log: Clicked on Edit link successfully (with screenshot)
[[12:13:30]] [SUCCESS] Screenshot refreshed successfully
[[12:13:30]] [SUCCESS] Screenshot refreshed
[[12:13:30]] [INFO] Refreshing screenshot...
[[12:13:27]] [INFO] Executing action 20/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:13:27]] [SUCCESS] Screenshot refreshed successfully
[[12:13:27]] [SUCCESS] Screenshot refreshed
[[12:13:26]] [INFO] Refreshing screenshot...
[[12:13:23]] [INFO] Executing action 19/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:13:23]] [SUCCESS] Screenshot refreshed successfully
[[12:13:23]] [SUCCESS] Screenshot refreshed
[[12:13:22]] [INFO] Refreshing screenshot...
[[12:13:19]] [INFO] Executing action 18/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:13:19]] [SUCCESS] Screenshot refreshed successfully
[[12:13:19]] [SUCCESS] Screenshot refreshed
[[12:13:18]] [INFO] Refreshing screenshot...
[[12:13:06]] [INFO] Executing action 17/25: Launch app: com.apple.Health
[[12:13:05]] [SUCCESS] Screenshot refreshed successfully
[[12:13:05]] [SUCCESS] Screenshot refreshed
[[12:13:04]] [SUCCESS] Screenshot refreshed successfully
[[12:13:04]] [INFO] Refreshing screenshot...
[[12:13:04]] [INFO] Skipping Hook Action during normal execution. This action will only be executed when a step fails.
[[12:13:04]] [INFO] Executing action 16/25: Hook Action: tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"] (Recovery)
[[12:13:04]] [SUCCESS] Screenshot refreshed
[[12:13:03]] [INFO] Refreshing screenshot...
[[12:13:02]] [SUCCESS] Screenshot refreshed successfully
[[12:13:02]] [SUCCESS] Screenshot refreshed
[[12:13:01]] [INFO] Refreshing screenshot...
[[12:12:59]] [INFO] Executing Multi Step action step 9/9: Add Log: App is closed (with screenshot)
[[12:12:59]] [SUCCESS] Screenshot refreshed successfully
[[12:12:59]] [SUCCESS] Screenshot refreshed
[[12:12:58]] [INFO] Refreshing screenshot...
[[12:12:54]] [INFO] Executing Multi Step action step 8/9: Terminate app: com.apple.Health
[[12:12:54]] [SUCCESS] Screenshot refreshed successfully
[[12:12:54]] [SUCCESS] Screenshot refreshed
[[12:12:53]] [INFO] Refreshing screenshot...
[[12:12:51]] [INFO] Executing Multi Step action step 7/9: Wait for 1 ms
[[12:12:50]] [SUCCESS] Screenshot refreshed successfully
[[12:12:50]] [SUCCESS] Screenshot refreshed
[[12:12:50]] [INFO] Refreshing screenshot...
[[12:12:48]] [INFO] Executing Multi Step action step 6/9: Add Log: Done link is clicked (with screenshot)
[[12:12:48]] [SUCCESS] Screenshot refreshed successfully
[[12:12:48]] [SUCCESS] Screenshot refreshed
[[12:12:47]] [INFO] Refreshing screenshot...
[[12:12:44]] [INFO] Executing Multi Step action step 5/9: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:12:44]] [SUCCESS] Screenshot refreshed successfully
[[12:12:44]] [SUCCESS] Screenshot refreshed
[[12:12:43]] [INFO] Refreshing screenshot...
[[12:12:42]] [INFO] Executing Multi Step action step 4/9: Add Log: Edit link is clicked (with screenshot)
[[12:12:41]] [SUCCESS] Screenshot refreshed successfully
[[12:12:41]] [SUCCESS] Screenshot refreshed
[[12:12:41]] [INFO] Refreshing screenshot...
[[12:12:38]] [INFO] Executing Multi Step action step 3/9: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:12:38]] [SUCCESS] Screenshot refreshed successfully
[[12:12:38]] [SUCCESS] Screenshot refreshed
[[12:12:37]] [INFO] Refreshing screenshot...
[[12:12:35]] [INFO] Executing Multi Step action step 2/9: Add Log: Launched App Successfully (with screenshot)
[[12:12:35]] [SUCCESS] Screenshot refreshed successfully
[[12:12:35]] [SUCCESS] Screenshot refreshed
[[12:12:34]] [INFO] Refreshing screenshot...
[[12:12:30]] [INFO] Executing Multi Step action step 1/9: Launch app: com.apple.Health
[[12:12:30]] [INFO] Loaded 9 steps from test case: health2
[[12:12:30]] [INFO] Loading steps for Multi Step action: health2
[[12:12:30]] [INFO] Executing action 15/25: Execute Test Case: health2 (9 steps)
[[12:12:29]] [SUCCESS] Screenshot refreshed successfully
[[12:12:29]] [SUCCESS] Screenshot refreshed
[[12:12:28]] [INFO] Refreshing screenshot...
[[12:12:26]] [INFO] Executing action 14/25: Add Log: Closed App Successfully (with screenshot)
[[12:12:25]] [SUCCESS] Screenshot refreshed successfully
[[12:12:25]] [SUCCESS] Screenshot refreshed
[[12:12:24]] [INFO] Refreshing screenshot...
[[12:12:21]] [INFO] Executing action 13/25: Terminate app: com.apple.Health
[[12:12:20]] [SUCCESS] Screenshot refreshed successfully
[[12:12:20]] [SUCCESS] Screenshot refreshed
[[12:12:20]] [INFO] Refreshing screenshot...
[[12:12:17]] [INFO] Executing action 12/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:12:16]] [SUCCESS] Screenshot refreshed successfully
[[12:12:16]] [SUCCESS] Screenshot refreshed
[[12:12:16]] [INFO] Refreshing screenshot...
[[12:12:13]] [INFO] Executing action 11/25: Tap on element with xpath: //XCUIElementTypeStaticText[@name="Edit"]
[[12:12:13]] [SUCCESS] Screenshot refreshed successfully
[[12:12:13]] [SUCCESS] Screenshot refreshed
[[12:12:12]] [INFO] Refreshing screenshot...
[[12:12:00]] [INFO] Executing action 10/25: Launch app: com.apple.Health
[[12:11:59]] [SUCCESS] Screenshot refreshed successfully
[[12:11:59]] [SUCCESS] Screenshot refreshed
[[12:11:58]] [INFO] Refreshing screenshot...
[[12:11:56]] [INFO] Executing action 9/25: Add Log: App is closed (with screenshot)
[[12:11:55]] [SUCCESS] Screenshot refreshed successfully
[[12:11:55]] [SUCCESS] Screenshot refreshed
[[12:11:54]] [INFO] Refreshing screenshot...
[[12:11:51]] [INFO] Executing action 8/25: Terminate app: com.apple.Health
[[12:11:51]] [SUCCESS] Screenshot refreshed successfully
[[12:11:51]] [SUCCESS] Screenshot refreshed
[[12:11:50]] [INFO] Refreshing screenshot...
[[12:11:47]] [INFO] Executing action 7/25: Wait for 1 ms
[[12:11:47]] [SUCCESS] Screenshot refreshed successfully
[[12:11:47]] [SUCCESS] Screenshot refreshed
[[12:11:46]] [INFO] Refreshing screenshot...
[[12:11:45]] [INFO] Executing action 6/25: Add Log: Done link is clicked (with screenshot)
[[12:11:44]] [SUCCESS] Screenshot refreshed successfully
[[12:11:44]] [SUCCESS] Screenshot refreshed
[[12:11:44]] [INFO] Refreshing screenshot...
[[12:11:41]] [INFO] Executing action 5/25: Click element: xpath= //XCUIElementTypeButton[@name="Done"]
[[12:11:40]] [SUCCESS] Screenshot refreshed successfully
[[12:11:40]] [SUCCESS] Screenshot refreshed
[[12:11:40]] [INFO] Refreshing screenshot...
[[12:11:38]] [INFO] Executing action 4/25: Add Log: Edit link is clicked (with screenshot)
[[12:11:38]] [SUCCESS] Screenshot refreshed successfully
[[12:11:38]] [SUCCESS] Screenshot refreshed
[[12:11:37]] [INFO] Refreshing screenshot...
[[12:11:35]] [INFO] Executing action 3/25: Click element: xpath=//XCUIElementTypeStaticText[@name="Edit"]
[[12:11:34]] [SUCCESS] Screenshot refreshed successfully
[[12:11:34]] [SUCCESS] Screenshot refreshed
[[12:11:34]] [INFO] Refreshing screenshot...
[[12:11:32]] [INFO] Executing action 2/25: Add Log: Launched App Successfully (with screenshot)
[[12:11:32]] [SUCCESS] Screenshot refreshed successfully
[[12:11:31]] [SUCCESS] Screenshot refreshed
[[12:11:31]] [INFO] Refreshing screenshot...
[[12:11:28]] [INFO] Executing action 1/25: Launch app: com.apple.Health
[[12:11:28]] [INFO] ExecutionManager: Starting execution of 25 actions...
[[12:11:28]] [WARNING] Error clearing screenshots: Unexpected token '<', "
        
[[12:11:28]] [INFO] Clearing screenshots from database before execution...
[[12:11:28]] [SUCCESS] All screenshots deleted successfully
[[12:11:28]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[12:11:28]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_121128/screenshots
[[12:11:28]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports/testsuite_execution_20250614_121128
[[12:11:28]] [SUCCESS] Report directory initialized successfully
[[12:11:28]] [INFO] Initializing report directory and screenshots folder...
[[12:11:25]] [SUCCESS] All screenshots deleted successfully
[[12:11:25]] [INFO] All actions cleared
[[12:11:25]] [INFO] Cleaning up screenshots...
[[12:11:20]] [SUCCESS] Connected to device: 00008120-00186C801E13C01E with AirTest support
[[12:11:20]] [INFO] Device info updated: 00008120-00186C801E13C01E
[[12:11:17]] [INFO] Connecting to device: 00008120-00186C801E13C01E (Platform: iOS)...
[[12:11:12]] [ERROR] Failed to connect to device
[[12:11:03]] [INFO] Connecting to device: 00008030-00020C123E60402E (Platform: iOS)...
[[12:11:00]] [SUCCESS] Found 2 device(s)
[[12:10:59]] [INFO] Refreshing device list...
