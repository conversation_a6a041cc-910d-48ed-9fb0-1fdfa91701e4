#!/usr/bin/env python3
"""
Test script to verify multi-device iproxy fix
This script simulates the multi-device scenario to ensure iproxy processes are managed correctly
"""

import subprocess
import time
import sys
import os

def check_port_forwarding_processes():
    """Check running iproxy and tidevice processes"""
    try:
        result = subprocess.run(['ps', 'aux'], capture_output=True, text=True, check=False)
        if result.returncode == 0:
            # Look for both iproxy and tidevice processes
            iproxy_lines = [line for line in result.stdout.split('\n') if 'iproxy' in line and 'grep' not in line]
            tidevice_lines = [line for line in result.stdout.split('\n') if 'tidevice' in line and 'relay' in line and 'grep' not in line]

            all_lines = iproxy_lines + tidevice_lines
            print(f"Found {len(all_lines)} port forwarding processes:")
            print(f"  - iproxy processes: {len(iproxy_lines)}")
            print(f"  - tidevice relay processes: {len(tidevice_lines)}")

            for line in all_lines:
                print(f"  {line}")
            return all_lines
        return []
    except Exception as e:
        print(f"Error checking processes: {e}")
        return []

def simulate_device_connection(device_id, wda_port):
    """Simulate connecting to a device with specific WDA port"""
    print(f"\n🔗 Simulating connection to device {device_id} on port {wda_port}")
    
    # Add the app directory to path
    app_dir = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'app')
    if app_dir not in sys.path:
        sys.path.insert(0, app_dir)
    
    # Import the device controller
    from utils.appium_device_controller import AppiumDeviceController
    
    # Create controller with specific WDA port
    controller = AppiumDeviceController(wda_port=wda_port)
    
    # Test the _start_iproxy method directly
    print(f"Starting iproxy for device {device_id} on port {wda_port}")
    process = controller._start_iproxy(device_id, local_port=wda_port, device_port=8100)
    
    if process:
        print(f"✅ iproxy started successfully for device {device_id}")
        return process
    else:
        print(f"❌ Failed to start iproxy for device {device_id}")
        return None

def main():
    print("🧪 Testing Multi-Device iproxy Fix")
    print("=" * 50)

    # Check initial state
    print("\n📊 Initial port forwarding processes:")
    initial_processes = check_port_forwarding_processes()

    # Use test ports that are likely to be free
    device1_id = "00008030-00020C123E60402E"  # iPhone12,8 from your screenshot
    device1_port = 9100  # Use test ports instead of real ones

    device2_id = "000008120-00186C801E13C01E"  # iPhone15,2 from your screenshot
    device2_port = 9101  # Use test ports instead of real ones

    # Check if test ports are free
    import socket
    for port in [device1_port, device2_port]:
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            if s.connect_ex(('localhost', port)) == 0:
                print(f"⚠️  Port {port} is already in use, test may not work correctly")

    # Simulate connecting first device
    process1 = simulate_device_connection(device1_id, device1_port)

    print("\n📊 After connecting device 1:")
    after_device1 = check_port_forwarding_processes()

    # Wait a moment
    time.sleep(2)

    # Simulate connecting second device
    process2 = simulate_device_connection(device2_id, device2_port)

    print("\n📊 After connecting device 2:")
    after_device2 = check_port_forwarding_processes()
    
    # Analysis
    print("\n🔍 Analysis:")
    print(f"Initial processes: {len(initial_processes)}")
    print(f"After device 1: {len(after_device1)}")
    print(f"After device 2: {len(after_device2)}")
    
    if len(after_device2) >= 2:
        print("✅ SUCCESS: Both devices should have their own port forwarding processes")
    else:
        print("❌ FAILURE: Second device connection killed first device's port forwarding")

    # Check if both ports are in use
    ports_in_use = []
    for line in after_device2:
        if str(device1_port) in line:
            ports_in_use.append(device1_port)
        if str(device2_port) in line:
            ports_in_use.append(device2_port)
    
    print(f"Ports in use: {ports_in_use}")
    
    if device1_port in ports_in_use and device2_port in ports_in_use:
        print("✅ SUCCESS: Both WDA ports are active")
    else:
        print("❌ FAILURE: Not all WDA ports are active")
    
    # Cleanup
    print("\n🧹 Cleaning up test processes...")
    if process1:
        try:
            process1.terminate()
            print("Terminated device 1 iproxy")
        except:
            pass
    
    if process2:
        try:
            process2.terminate()
            print("Terminated device 2 iproxy")
        except:
            pass
    
    print("\n✅ Test completed!")

if __name__ == "__main__":
    main()
