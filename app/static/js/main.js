// Main JavaScript file for Mobile App Automation Tool
// Uses fetch API instead of Socket.IO for all communication

// import ElementInteractions from './modules/ElementInteractions.js';

// Add element interactions module after DOM content loaded
document.addEventListener('DOMContentLoaded', function() {
    // ElementInteractions is now loaded via script tag in index.html
    console.log('DOM content loaded. Initializing App and modules...');

    // Ensure ElementInteractions class is available before creating the app instance
    if (typeof ElementInteractions === 'undefined') {
        console.error('ElementInteractions class not found. Ensure ElementInteractions.js is loaded before main.js.');
        alert('Critical Error: ElementInteractions module failed to load. Please check the script order in index.html and refresh.');
        return; // Stop initialization if the class isn't loaded
    }

    console.log('ElementInteractions module should be loaded globally.');

    // Create app instance but don't call init() since constructor does that
    const app = new AppiumAutomationApp();
    window.app = app; // Make app globally accessible

    // Initialize ElementInteractions - Now the class should be globally available
    try {
        app.elementInteractions = new ElementInteractions(app);
        console.log('ElementInteractions initialized successfully');
    } catch (error) {
        console.error('Failed to initialize ElementInteractions:', error);
        alert('Failed to initialize Element Interactions module. App may not function correctly.');
        // Decide if you want to stop execution or continue with reduced functionality
        // return;
    }

    // Load the TestCaseManager module via script tag
    // Ensure TestCaseManager class is available
    if (typeof TestCaseManager === 'undefined') {
         console.error('TestCaseManager class not found. Ensure TestCaseManager.js is loaded before main.js.');
         alert('Critical Error: TestCaseManager module failed to load. Please check the script order in index.html and refresh.');
         return;
    }

    console.log('TestCaseManager module should be loaded globally.');
    // Initialize TestCaseManager after app is created and module is loaded
    try {
        if (window.app) {
            window.app.testCaseManager = new TestCaseManager(window.app);
            console.log('TestCaseManager initialized successfully');
            // Now set up the Test Cases tab related event listeners that depend on TestCaseManager
            window.app.setupTestCaseEventListeners();
            // Initial load if the tab is already active might be needed here or handled by setup
            const testCasesTabButton = document.getElementById('test-cases-tab-btn');
            if (testCasesTabButton && testCasesTabButton.classList.contains('active')) {
                window.app.testCaseManager.loadAllTestCases();
            }
        }
    } catch (error) {
         console.error('Failed to initialize TestCaseManager:', error);
         alert('Failed to initialize Test Case Manager module. Test case functionality may be unavailable.');
    }


    // Automatically refresh the device list on page load
    if (window.app) {
        setTimeout(() => {
            console.log('Auto-refreshing device list on page load...');
            window.app.refreshDevices();
        }, 1000);
    }

    // Initialize random data generator
    if (window.randomDataGenerator) {
        window.randomDataGenerator.app = window.app;
        window.randomDataGenerator.init().then(() => {
            // Enhance existing input fields
            window.randomDataGenerator.enhanceAllInputFields();

            // Set up a mutation observer to enhance dynamically added input fields
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach((node) => {
                            if (node.nodeType === 1) { // Element node
                                // Find all text inputs in the added node
                                const inputs = node.querySelectorAll('input[type="text"]');
                                inputs.forEach((input) => {
                                    // Skip inputs that are already enhanced or should be excluded
                                    if (!input.closest('.input-group') && !input.hasAttribute('data-no-random-data')) {
                                        window.randomDataGenerator.enhanceInputField(input);
                                    }
                                });
                            }
                        });
                    }
                });
            });

            // Start observing the document body for changes
            observer.observe(document.body, { childList: true, subtree: true });
        });
    }

    // Set up click handlers after ElementInteractions is initialized (if needed)
    const loadBtn = document.getElementById("loadRecordingBtn");
    if (loadBtn) {
        loadBtn.addEventListener('click', function() {
            if (window.app) window.app.loadRecording();
        });
    }

    // Moved Test case tab 'shown' listener to setupTestCaseEventListeners

    // Initialize ExecutionManager after creating the app instance
    if (window.app && typeof ExecutionManager !== 'undefined') {
        try {
            window.executionManager = new ExecutionManager(window.app);
             console.log('ExecutionManager initialized successfully');
        } catch(error) {
             console.error('Failed to initialize ExecutionManager:', error);
             alert('Failed to initialize Execution Manager module. Execution functionality may be unavailable.');
        }
    } else if (typeof ExecutionManager === 'undefined') {
         console.error('ExecutionManager class not found. Ensure execution-manager.js is loaded before main.js.');
         alert('Critical Error: ExecutionManager module failed to load.');
    }

    // Initialize ActionManager after creating the app instance
     if (window.app && typeof ActionManager !== 'undefined') {
         try {
             // The instantiation is now done in the AppiumAutomationApp constructor
             // app.actionManager = new ActionManager(app);
             console.log('ActionManager should be initialized in App constructor.');
         } catch(error) {
             console.error('Failed to initialize ActionManager:', error);
             alert('Failed to initialize Action Manager module. Action management functionality may be unavailable.');
         }
     } else if (typeof ActionManager === 'undefined') {
          console.error('ActionManager class not found. Ensure action-manager.js is loaded before main.js.');
          alert('Critical Error: ActionManager module failed to load.');
     }

});

// Helper function to add a locator item to the list
function addLocatorItem(container, locator, type) {
    if (!container) {
        console.error("Container is null for locator:", locator, type);
        return;
    }

    const item = document.createElement('div');
    item.className = `locator-item ${type}`;
    item.textContent = locator;

    // Add copy badge
    const badge = document.createElement('span');
    badge.className = 'badge badge-primary';
    badge.innerHTML = '<i class="bi bi-clipboard"></i> Copy';
    item.appendChild(badge);

    // Add click to copy functionality
    item.addEventListener('click', function() {
        navigator.clipboard.writeText(locator).then(() => {
            // Visual feedback
            this.classList.add('copied');
            setTimeout(() => this.classList.remove('copied'), 1000);

            // Notification
            const notification = document.createElement('div');
            notification.className = 'copy-notification';
            notification.textContent = 'Locator copied to clipboard!';
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 3000);
        });
    });

    container.appendChild(item);
}

// Global variable to store the current inspected element
let currentInspectedElement = null;

// Main application class
class AppiumAutomationApp {
    constructor() {
        this.instanceId = Date.now() + '_' + Math.random(); // ADDED instanceId
        console.log(`[AppiumAutomationApp CONSTRUCTOR] New instance created with ID: ${this.instanceId}`); // ADDED Log

        this.isConnected = false;
        this.deviceId = null;
        this.isRecording = false;
        this.isExecuting = false;
        this.currentActions = [];
        this.pickingMode = null;
        this.pickingStartPoint = null;
        this.currentTestCaseName = null;
        this.currentTestCaseFilename = null;
        this.testCasesCache = []; // Managed by TestCaseManager, but keep ref here? Maybe remove?
        this.testCasesSort = 'date'; // Default sort, managed by TestCaseManager UI interactions
        this.isLoadingSuite = false; // Add this flag
        this.stopExecutionRequested = false; // Add stop flag
        this.actionManager = null; // Initialize ActionManager reference
        this.actionListEventListenerAttached = false; // Flag for event listener
        this.actionFormManager = new ActionFormManager(this);
        this.reportAndFormUtils = new ReportAndFormUtils(this);

        // Settings state
        this.settings = {
            testcases_dir: 'test_cases',
            reports_dir: 'reports'
        };

        // Initialize collapse/expand functionality
        this.initCollapseExpandFunctionality();

        if (typeof showLoading !== 'undefined') {
            this.showLoading = showLoading.bind(this);
            this.hideLoading = hideLoading.bind(this);
            this.showToast = showToast.bind(this);
            this.updateUIForConnectedState = updateUIForConnectedState.bind(this);
            this.disableInteractions = disableInteractions.bind(this);
            this.enableInteractions = enableInteractions.bind(this);
        } else {
            console.error('UI utility functions are not loaded. Ensure uiUtils.js is included.');
        }

        // Initialize UI elements
        this.deviceSelect = document.getElementById('deviceSelect');
        this.connectBtn = document.getElementById('connectBtn');
        this.refreshDevicesBtn = document.getElementById('refreshDevices');
        this.deviceScreen = document.getElementById('deviceScreen');
        this.refreshScreenBtn = document.getElementById('refreshScreenBtn');
        this.webInspectorBtn = document.getElementById('webInspectorBtn');
        this.actionList = document.getElementById('actionsList');
        this.addActionBtn = document.getElementById('addAction');
        this.clearActionsBtn = document.getElementById('clearActions');
        this.actionTypeSelect = document.getElementById('actionType');
        this.actionForms = document.querySelectorAll('.action-form');
        this.executeActionsBtn = document.getElementById('executeActions');
        this.stopExecutionBtn = document.getElementById('stopExecution');
        this.loadRecordingBtn = document.getElementById('loadRecordingBtn');
        this.saveRecordingBtn = document.getElementById('saveRecordingBtn');
        this.saveAsRecordingBtn = document.getElementById('saveAsRecordingBtn');
        this.recordBtn = document.getElementById('startRecording');
        this.stopRecordingBtn = document.getElementById('stopRecording');
        this.clearLogBtn = document.getElementById('clearLogBtn');
        this.actionLog = document.getElementById('actionLog'); // Ensure actionLog is initialized here
        this.refreshTestCasesBtn = document.getElementById('refreshTestCasesBtn');
        this.testCaseSearchInput = document.getElementById('testCaseSearch');
        this.clearTestCaseSearchBtn = document.getElementById('clearTestCaseSearchBtn');
        this.sortByNameBtn = document.getElementById('sortByNameBtn');
        this.sortByDateBtn = document.getElementById('sortByDateBtn');

        // Settings elements
        this.testCasesDirInput = document.getElementById('testCasesDir');
        this.reportsDirInput = document.getElementById('reportsDir');
        this.browseTestCasesBtn = document.getElementById('browseTestCasesBtn');
        this.browseReportsBtn = document.getElementById('browseReportsBtn');
        this.resetSettingsBtn = document.getElementById('resetSettingsBtn');
        this.saveSettingsBtn = document.getElementById('saveSettingsBtn');
        this.testCasesDirPicker = document.getElementById('testCasesDirInput');
        this.reportsDirPicker = document.getElementById('reportsDirInput');

        // Initialize element interactions module
        this.elementInteractions = null; // Will be set after module loads globally

        // Initialize test case manager module
        this.testCaseManager = null; // Will be set after module loads globally

        // Initialize multi step action module
        this.multiStepAction = null; // Will be set after module loads globally
        this.repeatStepsAction = null; // Will be set after module loads globally

        // Initialize hook action module
        this.hookAction = null; // Will be set after module loads globally

        // Initialize fallback locators module
        this.fallbackLocatorsManager = null; // Will be set after module loads globally

        // Instantiate ActionManager here, passing the app instance
        if (typeof ActionManager !== 'undefined') {
            try {
                this.actionManager = new ActionManager(this);
                console.log('ActionManager instantiated in App constructor.');
            } catch (error) {
                 console.error('Failed to instantiate ActionManager in constructor:', error);
                 alert('Failed to initialize Action Manager. Action functionality may be limited.');
            }
        } else {
            console.error('ActionManager class not defined when App constructor ran.');
             alert('Critical Error: ActionManager class not available during app initialization.');
        }

        // Instantiate MultiStepAction here, passing the app instance
        if (typeof MultiStepAction !== 'undefined') {
            try {
                this.multiStepAction = new MultiStepAction(this);
                console.log('MultiStepAction instantiated in App constructor.');
            } catch (error) {
                 console.error('Failed to instantiate MultiStepAction in constructor:', error);
                 alert('Failed to initialize Multi Step Action. Multi Step functionality may be limited.');
            }
        } else {
            console.error('MultiStepAction class not defined when App constructor ran.');
        }

        // Instantiate RepeatStepsAction here, passing the app instance
        if (typeof RepeatStepsAction !== 'undefined') {
            try {
                this.repeatStepsAction = new RepeatStepsAction(this);
                console.log('RepeatStepsAction instantiated in App constructor.');
            } catch (error) {
                 console.error('Failed to instantiate RepeatStepsAction in constructor:', error);
                 alert('Failed to initialize Repeat Steps Action. Repeat Steps functionality may be limited.');
            }
        } else {
            console.error('RepeatStepsAction class not defined when App constructor ran.');
        }

        // Instantiate HookAction here, passing the app instance
        if (typeof HookAction !== 'undefined') {
            try {
                this.hookAction = new HookAction(this);
                window.hookAction = this.hookAction; // Make it globally accessible
                console.log('HookAction instantiated in App constructor.');
            } catch (error) {
                 console.error('Failed to instantiate HookAction in constructor:', error);
                 alert('Failed to initialize Hook Action. Hook Action functionality may be limited.');
            }
        } else {
            console.error('HookAction class not defined when App constructor ran.');
        }

        // Instantiate FallbackLocatorsManager here, passing the app instance
        if (typeof FallbackLocatorsManager !== 'undefined') {
            try {
                this.fallbackLocatorsManager = new FallbackLocatorsManager(this);
                console.log('FallbackLocatorsManager instantiated in App constructor.');
            } catch (error) {
                 console.error('Failed to instantiate FallbackLocatorsManager in constructor:', error);
                 alert('Failed to initialize Fallback Locators Manager. Fallback locator functionality may be limited.');
            }
        } else {
            console.error('FallbackLocatorsManager class not defined when App constructor ran.');
        }

        // Instantiate TapFallbackManager here, passing the app instance
        if (typeof TapFallbackManager !== 'undefined') {
            try {
                this.tapFallbackManager = new TapFallbackManager(this);
                console.log('TapFallbackManager instantiated in App constructor.');
            } catch (error) {
                 console.error('Failed to instantiate TapFallbackManager in constructor:', error);
                 alert('Failed to initialize Tap Fallback Manager. Tap fallback functionality may be limited.');
            }
        } else {
            console.error('TapFallbackManager class not defined when App constructor ran.');
        }

        // Initialize the application
        this.init();

        // Initialize action forms to ensure all fields are visible
        // this.initActionForms(); // Removed: updateActionForm called in init via setupEventListeners
    }

    init() {
        console.log('Initializing Appium Automation App...');

        // Initialize UI elements (references)
        this.deviceSelect = document.getElementById('deviceSelect');
        this.connectBtn = document.getElementById('connectBtn');
        this.refreshDevicesBtn = document.getElementById('refreshDevices');
        this.deviceScreen = document.getElementById('deviceScreen');
        this.refreshScreenBtn = document.getElementById('refreshScreenBtn');
        this.inspectElementBtn = document.getElementById('inspectElementBtn');
        this.webInspectorBtn = document.getElementById('webInspectorBtn');
        this.actionList = document.getElementById('actionsList');
        this.addActionBtn = document.getElementById('addAction');
        this.clearActionsBtn = document.getElementById('clearActions');
        this.actionTypeSelect = document.getElementById('actionType');
        this.actionForms = document.querySelectorAll('.action-form');
        this.executeActionsBtn = document.getElementById('executeActions');
        this.stopExecutionBtn = document.getElementById('stopExecution');
        this.loadRecordingBtn = document.getElementById('loadRecordingBtn');
        this.saveRecordingBtn = document.getElementById('saveRecordingBtn');
        this.saveAsRecordingBtn = document.getElementById('saveAsRecordingBtn');
        this.recordBtn = document.getElementById('startRecording');
        this.stopRecordingBtn = document.getElementById('stopRecording');
        this.clearLogBtn = document.getElementById('clearLogBtn');
        this.refreshTestCasesBtn = document.getElementById('refreshTestCasesBtn');
        this.testCaseSearchInput = document.getElementById('testCaseSearch');
        this.clearTestCaseSearchBtn = document.getElementById('clearTestCaseSearchBtn');
        this.sortByNameBtn = document.getElementById('sortByNameBtn');
        this.sortByDateBtn = document.getElementById('sortByDateBtn');
        this.actionLog = document.getElementById('actionLog'); // Ensure actionLog is initialized here

        // Settings elements
        this.testCasesDirInput = document.getElementById('testCasesDir');
        this.reportsDirInput = document.getElementById('reportsDir');
        this.browseTestCasesBtn = document.getElementById('browseTestCasesBtn');
        this.browseReportsBtn = document.getElementById('browseReportsBtn');
        this.resetSettingsBtn = document.getElementById('resetSettingsBtn');
        this.saveSettingsBtn = document.getElementById('saveSettingsBtn');
        this.testCasesDirPicker = document.getElementById('testCasesDirInput');
        this.reportsDirPicker = document.getElementById('reportsDirInput');

        // Initialize state
        this.isConnected = false;
        this.isRecording = false;
        this.isExecuting = false;
        this.currentDevice = null;
        this.currentActions = [];
        this.currentTestCaseName = '';
        this.currentTestCaseFilename = '';
        this.deviceInfo = null;
        this.selectedSortBy = 'date'; // Default sort - controlled via TestCaseManager now
        this.testCasesCache = []; // Initialize as empty array - Data managed by TestCaseManager
        this.isLoadingSuite = false;
        this.stopExecutionRequested = false; // Initialize stop flag

        // Call the method to set up event listeners
        this.setupEventListeners(); // Basic listeners
        // Test case listeners are set up after TestCaseManager loads

        // Initialize other UI components
        this.initSortable();
        this.initCollapseExpandFunctionality();
        this.setupActionListEventDelegation(); // Add call to new method
        // Trigger initial form update after listeners are set
        if (this.actionFormManager) {
            this.actionFormManager.updateActionForm();
        } else {
            console.error("ActionFormManager not initialized in init()");
        }

        // Load reference images
        this.loadReferenceImages('tap');
        this.loadReferenceImages('doubleTap');
        this.loadReferenceImages('clickImageAirtest');
        this.loadReferenceImages('waitImageAirtest');
        this.loadReferenceImages('doubleClickImageAirtest');

        console.log('Appium Automation App initialized');
    }

    setupActionListEventDelegation() {
        if (this.actionListEventListenerAttached) {
            return; // Listener already attached
        }

        const actionsListElement = document.getElementById('actionsList');
        if (actionsListElement) {
            actionsListElement.addEventListener('click', async (event) => {
                const removeButton = event.target.closest('.remove-test-case');
                const retryButton = event.target.closest('.retry-test-case');

                if (removeButton) {
                    const testCaseContainer = removeButton.closest('.test-case-container');
                    if (testCaseContainer) {
                        const tcFilename = testCaseContainer.dataset.filename;
                        const tcName = testCaseContainer.dataset.testCaseName;
                        if (confirm(`Are you sure you want to remove test case "${tcName || tcFilename}" from this list? This does not delete the test case file.`)) {
                            testCaseContainer.remove();
                            this.showToast('Test Case Removed', `"${tcName || tcFilename}" removed from the current view.`, 'info');
                            // Note: this.currentActions will be out of sync until the suite is saved or reloaded.
                            // A more robust implementation would splice this.currentActions and re-index.
                            this.updateExecutionButtons(); // Update button states
                        }
                    }
                } else if (retryButton) {
                    const testCaseContainer = retryButton.closest('.test-case-container');
                    if (testCaseContainer) {
                        const tcFilename = retryButton.dataset.filename; // Get from button directly
                        const tcName = retryButton.dataset.testCaseName; // Get from button directly

                        if (!tcFilename) {
                            console.error('Retry button clicked but filename is missing from data-attribute.');
                            this.showToast('Retry Failed', 'Cannot retry: Test case filename is missing.', 'error');
                            return;
                        }

                        console.log(`Retry button clicked for test case: ${tcName} (File: ${tcFilename})`);
                        this.showToast('Retrying Test Case', `Attempting to retry \"${tcName || tcFilename}\"...`, 'info');

                        // Disable the retry button for this specific test case and show spinner
                        retryButton.disabled = true;
                        const originalButtonText = retryButton.innerHTML;
                        retryButton.innerHTML = '<span class=\"spinner-border spinner-border-sm\" role=\"status\" aria-hidden=\"true\"></span> Retrying...';

                        // Find all action items within this specific test case container
                        const actionItems = testCaseContainer.querySelectorAll('.action-item');
                        let allRetriesSuccessful = true;

                        // Clear previous statuses from these action items
                        actionItems.forEach(item => {
                            item.classList.remove('success', 'error', 'executing');
                            const spinner = item.querySelector('.spinner-border');
                            if (spinner) spinner.remove();
                            const statusIcon = item.querySelector('.bi-check-circle-fill, .bi-x-circle-fill');
                            if (statusIcon) statusIcon.remove();
                        });

                        try {
                            for (const actionItem of actionItems) {
                                const globalActionIndex = parseInt(actionItem.dataset.actionIndex);
                                if (isNaN(globalActionIndex) || globalActionIndex < 0 || globalActionIndex >= this.currentActions.length) {
                                    console.error(`Invalid globalActionIndex ${globalActionIndex} for action item:`, actionItem);
                                    this.logAction('error', `Skipping action due to invalid index ${globalActionIndex} during retry.`);
                                    allRetriesSuccessful = false;
                                    actionItem.classList.add('error'); 
                                    const actionContent = actionItem.querySelector('.action-content');
                                    if (actionContent && !actionContent.querySelector('.bi-x-circle-fill')) {
                                        const errorIcon = document.createElement('i');
                                        errorIcon.className = 'bi bi-x-circle-fill text-danger me-2';
                                        actionContent.prepend(errorIcon);
                                    }
                                    continue; 
                                }
                                const actionData = this.currentActions[globalActionIndex];
                                
                                // If this is a multiStep action, make sure the container is expanded
                                // so we can see its child steps during retry
                                if (actionData.type === 'multiStep') {
                                    const multiStepContainer = actionItem.querySelector('.multi-step-container');
                                    const toggleButton = actionItem.querySelector('.toggle-multi-step');
                                    
                                    // If container exists but isn't displayed, expand it
                                    if (multiStepContainer && multiStepContainer.style.display === 'none' && toggleButton) {
                                        toggleButton.click(); // This will expand the container
                                        
                                        // If steps aren't loaded yet, we need to wait for them
                                        if (!actionData.steps_loaded) {
                                            await new Promise(resolve => {
                                                // Check every 100ms if steps are loaded
                                                const checkInterval = setInterval(() => {
                                                    if (actionData.steps_loaded) {
                                                        clearInterval(checkInterval);
                                                        resolve();
                                                    }
                                                }, 100);
                                                
                                                // Timeout after 5 seconds
                                                setTimeout(() => {
                                                    clearInterval(checkInterval);
                                                    resolve();
                                                }, 5000);
                                            });
                                        }
                                    }
                                }
                                
                                const actionSuccess = await this.playAction(actionData, globalActionIndex, actionItem);
                                
                                if (!actionSuccess) {
                                    allRetriesSuccessful = false;
                                    // playAction handles its own UI for error state on the item.
                                }
                                
                                // If this was a multiStep action, update all step statuses
                                if (actionData.type === 'multiStep') {
                                    const multiStepContainer = actionItem.querySelector('.multi-step-container');
                                    if (multiStepContainer && actionSuccess) {
                                        // For successful multi-step, mark all steps as success
                                        const stepItems = multiStepContainer.querySelectorAll('.multi-step-item');
                                        stepItems.forEach(stepItem => {
                                            const stepStatus = stepItem.querySelector('.multi-step-status');
                                            if (stepStatus) {
                                                stepStatus.innerHTML = '<i class="bi bi-check-circle text-success"></i>';
                                            }
                                            stepItem.classList.remove('multi-step-executing', 'multi-step-error');
                                            stepItem.classList.add('multi-step-success');
                                        });
                                    }
                                }
                            }
                        } catch (error) {
                            console.error('Error during test case retry process:', error);
                            this.showToast('Retry Error', `An error occurred during the retry of \"${tcName || tcFilename}\": ${error.message}`, 'error');
                            allRetriesSuccessful = false;
                        }

                        // Re-enable the retry button and restore its text
                        retryButton.disabled = false;
                        retryButton.innerHTML = originalButtonText;

                        if (allRetriesSuccessful) {
                            this.showToast('Retry Complete', `\"${tcName || tcFilename}\" re-executed successfully.`, 'success');
                            
                            // Update test case header to show success status
                            const header = testCaseContainer.querySelector('.test-case-header');
                            if (header) {
                                header.classList.remove('error');
                                header.classList.add('success');
                            }
                        } else {
                            this.showToast('Retry Complete', `\"${tcName || tcFilename}\" re-executed with some errors. Please check action logs.`, 'warning');
                            
                            // Update test case header to show error status
                            const header = testCaseContainer.querySelector('.test-case-header');
                            if (header) {
                                header.classList.remove('success');
                                header.classList.add('error');
                            }
                        }

                        // Reports refresh code removed as it's no longer needed
                    }
                }
            });
            this.actionListEventListenerAttached = true;
            console.log("Event listener for retry/remove on actionsList attached.");
        } else {
            console.error("Could not attach event listener: actionsList element not found.");
        }
    }

    /**
     * Initialize the collapse/expand functionality for the Action Builder section
     */
    initCollapseExpandFunctionality() {
        // Remove the DOMContentLoaded listener to avoid duplicates
            const collapseToggle = document.querySelector('.collapse-toggle');
            const actionBuilderCollapse = document.getElementById('actionBuilderCollapse');

            if (collapseToggle && actionBuilderCollapse) {
                // Bootstrap collapse events
                actionBuilderCollapse.addEventListener('hidden.bs.collapse', () => {
                    // Just rely on CSS for the icon animation
                    console.log('Action Builder collapsed');
                });

                actionBuilderCollapse.addEventListener('shown.bs.collapse', () => {
                    // Just rely on CSS for the icon animation
                    console.log('Action Builder expanded');
                });
            }
    }

    async checkPersistentConnection() {
        const storedDeviceId = sessionStorage.getItem('connectedDeviceId');
        if (storedDeviceId) {
            this.logAction('info', `Found stored device ID: ${storedDeviceId}. Checking status...`);
            try {
                // Ask backend if this device is still considered connected
                const result = await this.fetchApi('device/status', 'POST', { device_id: storedDeviceId });

                if (result.status === 'connected') {
                    this.logAction('success', `Re-established connection state for ${storedDeviceId}`);
                    this.isConnected = true;
                    this.deviceId = storedDeviceId;

                    // Update UI to reflect connected state
                    this.connectBtn.textContent = 'Disconnect';
                    this.connectBtn.classList.replace('btn-primary', 'btn-danger'); // Assuming primary is default
                    this.updateUIForConnectedState(true);

                    // Optionally refresh the screenshot
                    this.refreshScreenshot();

                    // Update device select dropdown to show the connected device
                    this.selectDeviceById(storedDeviceId, false); // Pass false to prevent reconnecting

                } else {
                    this.logAction('warning', `Server reports device ${storedDeviceId} is not connected. Clearing stored state.`);
                    sessionStorage.removeItem('connectedDeviceId');
                }
            } catch (error) {
                this.logAction('error', `Failed to check persistent connection status: ${error.message}`);
                sessionStorage.removeItem('connectedDeviceId'); // Clear on error
            } finally {
                // Hide loading overlay after attempt (success or failure)
                this.hideLoading();
            }
        }
    }

    initSortable() {
        const actionsList = document.getElementById('actionsList');
        if (actionsList) {
            console.log('Initializing Sortable on actionsList element');

            // Destroy existing sortable instance if it exists
            if (this.sortable) {
                this.sortable.destroy();
            }

            // Add visual indicator for draggable items
            const items = actionsList.querySelectorAll('.list-group-item');
            items.forEach(item => {
                // Make sure each item has a drag indicator
                const actionContent = item.querySelector('.action-content');
                if (actionContent && !actionContent.querySelector('.drag-indicator')) {
                    const dragIndicator = document.createElement('i');
                    dragIndicator.className = 'bi bi-grip-vertical drag-indicator me-2';
                    dragIndicator.style.cursor = 'grab';
                    actionContent.insertBefore(dragIndicator, actionContent.firstChild);
                }
            });

            // Make the actions list sortable
            try {
                this.sortable = new Sortable(actionsList, {
                    animation: 150,  // Animation speed (ms)
                    handle: '.drag-indicator',  // Drag handle (use the icon specifically)
                    ghostClass: 'sortable-ghost',  // Class name for the drop placeholder
                    chosenClass: 'sortable-chosen',  // Class name for the chosen item
                    dragClass: 'sortable-drag',  // Class name for the dragging item
                    forceFallback: false,  // Use native HTML5 DnD when possible
                    fallbackTolerance: 3,  // Tolerance before starting drag

                    // Event fired when the order changes
                    onEnd: (evt) => {
                        // Update the actions array to match the new order
                        const newOrder = [];
                        const actionItems = actionsList.querySelectorAll('.list-group-item');

                        actionItems.forEach((item, index) => {
                            const originalIndex = parseInt(item.dataset.actionIndex);
                            if (!isNaN(originalIndex) && this.currentActions[originalIndex]) {
                                newOrder.push(this.currentActions[originalIndex]);
                                // Update the data-action-index to reflect the new position
                                item.dataset.actionIndex = index.toString();
                            }
                        });

                        // Update the current actions with the new order
                        if (newOrder.length > 0) {
                            this.currentActions = newOrder;

                            // Update step numbers to reflect the new order
                            this.updateStepNumbers();

                            // Save updated actions to server
                            // this.saveActionsToServer(); // Commented out to prevent 404
                        }
                    }
                });

                console.log('Sortable initialized successfully');
            } catch (error) {
                console.error('Failed to initialize Sortable:', error);
            }
        } else {
            console.error('Failed to find actionsList element for Sortable');
        }
    }

    setupEventListeners() {
        // Set up device selection and connection
        if (this.refreshDevicesBtn) {
            this.refreshDevicesBtn.addEventListener('click', () => this.refreshDevices());
        }

        if (this.connectBtn) {
            this.connectBtn.addEventListener('click', () => this.connectToDevice());
        }

        // Setup screenshot refresh
        if (this.refreshScreenBtn) {
            this.refreshScreenBtn.addEventListener('click', () => this.refreshScreenshot());
        }

        // Add event listener for the clear log button
        if (this.clearLogBtn) {
            this.clearLogBtn.addEventListener('click', () => this.clearActionLog());
        }

        // Add event listener for Web Inspector button
        if (this.webInspectorBtn) {
            this.webInspectorBtn.addEventListener('click', () => this.openWebInspector());
        }

        // Add event listener for device screen to handle clicks
        if (this.deviceScreen) {
            this.deviceScreen.addEventListener('click', (event) => this.handleScreenClick(event));
        }

        // Add event listeners for image capture buttons
        const captureScreenImageBtn = document.getElementById('captureScreenImage');
        if (captureScreenImageBtn) {
            captureScreenImageBtn.addEventListener('click', () => this.startImageCapture('tap'));
        }

        // Add event listener for Tap and Type coordinate picker
        const pickTapAndTypeCoordinatesBtn = document.getElementById('pickTapAndTypeCoordinates');
        if (pickTapAndTypeCoordinatesBtn) {
            pickTapAndTypeCoordinatesBtn.addEventListener('click', () => {
                if (!this.isConnected) {
                    this.logAction('error', 'Cannot pick coordinates - no device connected');
                    return;
                }

                this.logAction('info', 'Click on the device screen to select coordinates for Tap and Type action');
                this.pickingMode = 'tapAndType';

                // Change cursor to indicate picking mode
                if (this.deviceScreen) {
                    this.deviceScreen.style.cursor = 'crosshair';
                }

                // Change button text to indicate picking mode
                pickTapAndTypeCoordinatesBtn.textContent = 'Cancel Picking';
                pickTapAndTypeCoordinatesBtn.classList.replace('btn-outline-primary', 'btn-outline-danger');

                // Change the click handler to cancel picking mode
                const cancelPickingHandler = () => {
                    this.pickingMode = null;
                    if (this.deviceScreen) {
                        this.deviceScreen.style.cursor = 'default';
                    }
                    pickTapAndTypeCoordinatesBtn.textContent = 'Pick from Screen';
                    pickTapAndTypeCoordinatesBtn.classList.replace('btn-outline-danger', 'btn-outline-primary');

                    // Remove this event listener and restore the original one
                    pickTapAndTypeCoordinatesBtn.removeEventListener('click', cancelPickingHandler);
                    pickTapAndTypeCoordinatesBtn.addEventListener('click', cancelPickingHandler);
                };

                pickTapAndTypeCoordinatesBtn.removeEventListener('click', pickTapAndTypeCoordinatesBtn.onclick);
                pickTapAndTypeCoordinatesBtn.addEventListener('click', cancelPickingHandler);
            });
        }

        const captureDoubleTapScreenImageBtn = document.getElementById('captureDoubleTapScreenImage');
        if (captureDoubleTapScreenImageBtn) {
            captureDoubleTapScreenImageBtn.addEventListener('click', () => this.startImageCapture('doubleTap'));
        }

        const captureScreenContainsImageBtn = document.getElementById('captureScreenContainsImage');
        if (captureScreenContainsImageBtn) {
            captureScreenContainsImageBtn.addEventListener('click', () => this.startImageCapture('screenContains'));
        }

        // Add event listeners for the new Airtest image capture buttons
        const captureClickImageAirtestScreenImageBtn = document.getElementById('captureClickImageAirtestScreenImage');
        if (captureClickImageAirtestScreenImageBtn) {
            captureClickImageAirtestScreenImageBtn.addEventListener('click', () => this.startImageCapture('clickImageAirtest'));
        }

        const captureWaitImageAirtestScreenImageBtn = document.getElementById('captureWaitImageAirtestScreenImage');
        if (captureWaitImageAirtestScreenImageBtn) {
            captureWaitImageAirtestScreenImageBtn.addEventListener('click', () => this.startImageCapture('waitImageAirtest'));
        }

        const captureDoubleClickImageAirtestScreenImageBtn = document.getElementById('captureDoubleClickImageAirtestScreenImage');
        if (captureDoubleClickImageAirtestScreenImageBtn) {
            captureDoubleClickImageAirtestScreenImageBtn.addEventListener('click', () => this.startImageCapture('doubleClickImageAirtest'));
        }

        // Add event listener for Tap If Image Exists capture button
        const captureTapIfImageExistsScreenImageBtn = document.getElementById('captureTapIfImageExistsScreenImage');
        if (captureTapIfImageExistsScreenImageBtn) {
            captureTapIfImageExistsScreenImageBtn.addEventListener('click', () => this.startImageCapture('tapIfImageExists'));
        }

        // Add event listeners for refresh image buttons
        const refreshTapImagesBtn = document.getElementById('refreshTapImages');
        if (refreshTapImagesBtn) {
            refreshTapImagesBtn.addEventListener('click', () => this.loadReferenceImages('tap'));
        }

        const refreshDoubleTapImagesBtn = document.getElementById('refreshDoubleTapImages');
        if (refreshDoubleTapImagesBtn) {
            refreshDoubleTapImagesBtn.addEventListener('click', () => this.loadReferenceImages('doubleTap'));
        }

        // Removed duplicate declaration of refreshTapIfImageExistsImagesBtn

        const refreshScreenContainsImagesBtn = document.getElementById('refreshScreenContainsImages');
        if (refreshScreenContainsImagesBtn) {
            refreshScreenContainsImagesBtn.addEventListener('click', () => this.loadReferenceImages('ifScreenContains'));
        }

        // Add event listeners for the new Airtest refresh image buttons
        const refreshClickImageAirtestImagesBtn = document.getElementById('refreshClickImageAirtestImages');
        if (refreshClickImageAirtestImagesBtn) {
            refreshClickImageAirtestImagesBtn.addEventListener('click', () => this.loadReferenceImages('clickImageAirtest'));
        }

        const refreshWaitImageAirtestImagesBtn = document.getElementById('refreshWaitImageAirtestImages');
        if (refreshWaitImageAirtestImagesBtn) {
            refreshWaitImageAirtestImagesBtn.addEventListener('click', () => this.loadReferenceImages('waitImageAirtest'));
        }

        const refreshDoubleClickImageAirtestImagesBtn = document.getElementById('refreshDoubleClickImageAirtestImages');
        if (refreshDoubleClickImageAirtestImagesBtn) {
            refreshDoubleClickImageAirtestImagesBtn.addEventListener('click', () => this.loadReferenceImages('doubleClickImageAirtest'));
        }

        const refreshTapIfImageExistsImagesBtn = document.getElementById('refreshTapIfImageExistsImages');
        if (refreshTapIfImageExistsImagesBtn) {
            refreshTapIfImageExistsImagesBtn.addEventListener('click', () => this.loadReferenceImages('tap', 'tapIfImageExistsFilename'));
        }

        // Add event listeners for Wait Till Element image buttons
        const captureWaitTillScreenImageBtn = document.getElementById('captureWaitTillScreenImage');
        if (captureWaitTillScreenImageBtn) {
            captureWaitTillScreenImageBtn.addEventListener('click', () => this.startImageCapture('waitTill'));
        }

        const refreshWaitTillImagesBtn = document.getElementById('refreshWaitTillImages');
        if (refreshWaitTillImagesBtn) {
            refreshWaitTillImagesBtn.addEventListener('click', () => this.loadReferenceImages('waitTill'));
        }

        // Add event listeners for form inputs that need coordinates from screen
        const pickTapCoordinatesBtn = document.getElementById('pickTapCoordinates');
        if (pickTapCoordinatesBtn) {
            pickTapCoordinatesBtn.addEventListener('click', () => this.enablePickMode('tap'));
        }

        const pickDoubleTapCoordinatesBtn = document.getElementById('pickDoubleTapCoordinates');
        if (pickDoubleTapCoordinatesBtn) {
            pickDoubleTapCoordinatesBtn.addEventListener('click', () => this.enablePickMode('doubleTap'));
        }

        const pickSwipeCoordinatesBtn = document.getElementById('pickSwipeCoordinates');
        if (pickSwipeCoordinatesBtn) {
            pickSwipeCoordinatesBtn.addEventListener('click', () => this.enablePickMode('swipe'));
        }

        const pickDoubleClickCoordinatesBtn = document.getElementById('pickDoubleClickCoordinates');
        if (pickDoubleClickCoordinatesBtn) {
            pickDoubleClickCoordinatesBtn.addEventListener('click', () => this.enablePickMode('doubleClick'));
        }

        // Add event listener for action type change
        if (this.actionTypeSelect) {
            this.actionTypeSelect.addEventListener('change', () => {
                // this.updateActionForm(); // CHANGED to use ActionFormManager
                if (this.actionFormManager) {
                    this.actionFormManager.updateActionForm();
                } else {
                    console.error("ActionFormManager not available in actionTypeSelect listener");
                }
            });
        }

        // Add event listener for Add Fallback Locator button for tap
        const addTapFallbackLocatorBtn = document.getElementById('addTapFallbackLocator');
        if (addTapFallbackLocatorBtn) {
            addTapFallbackLocatorBtn.addEventListener('click', () => {
                if (this.fallbackLocatorsManager) {
                    this.fallbackLocatorsManager.addFallbackLocator('tap');
                } else {
                    console.error('FallbackLocatorsManager not initialized');
                }
            });
        }

        // Add event listener for Add Fallback Locator button for doubleTap
        const addDoubleTapFallbackLocatorBtn = document.getElementById('addDoubleTapFallbackLocator');
        if (addDoubleTapFallbackLocatorBtn) {
            addDoubleTapFallbackLocatorBtn.addEventListener('click', () => {
                if (this.fallbackLocatorsManager) {
                    this.fallbackLocatorsManager.addFallbackLocator('doubleTap');
                } else {
                    console.error('FallbackLocatorsManager not initialized');
                }
            });
        }

        // Add event listener for swipe direction change
        const swipeDirectionSelect = document.getElementById('swipeDirection');
        if (swipeDirectionSelect) {
            swipeDirectionSelect.addEventListener('change', () => {
                const swipeCustomSettings = document.getElementById('swipeCustomSettings');
                const direction = swipeDirectionSelect.value;

                // Set predefined coordinates based on direction
                if (direction !== 'custom') {
                    // Keep the custom settings visible for all directions
                    // This allows users to see and further adjust the coordinates

                    // Get references to range sliders
                    const startXSlider = document.getElementById('swipeStartX');
                    const startYSlider = document.getElementById('swipeStartY');
                    const endXSlider = document.getElementById('swipeEndX');
                    const endYSlider = document.getElementById('swipeEndY');

                    // Get references to numeric inputs
                    const startXInput = document.getElementById('swipeStartXInput');
                    const startYInput = document.getElementById('swipeStartYInput');
                    const endXInput = document.getElementById('swipeEndXInput');
                    const endYInput = document.getElementById('swipeEndYInput');

                    // Get references to display value spans
                    const startXValueSpan = document.getElementById('swipeStartXValue');
                    const startYValueSpan = document.getElementById('swipeStartYValue');
                    const endXValueSpan = document.getElementById('swipeEndXValue');
                    const endYValueSpan = document.getElementById('swipeEndYValue');

                    // Set predefined relative coordinates (values are in percentage 0-100)
                    switch(direction) {
                        case 'up':
                            // Swipe from bottom center to top center
                            startXSlider.value = 50; // 0.5 relative
                            startYSlider.value = 70; // 0.7 relative
                            endXSlider.value = 50;   // 0.5 relative
                            endYSlider.value = 30;   // 0.3 relative
                            break;
                        case 'down':
                            // Swipe from top center to bottom center
                            startXSlider.value = 50; // 0.5 relative
                            startYSlider.value = 30; // 0.3 relative
                            endXSlider.value = 50;   // 0.5 relative
                            endYSlider.value = 70;   // 0.7 relative
                            break;
                        case 'left':
                            // Swipe from right center to left center
                            startXSlider.value = 70; // 0.7 relative
                            startYSlider.value = 50; // 0.5 relative
                            endXSlider.value = 30;   // 0.3 relative
                            endYSlider.value = 50;   // 0.5 relative
                            break;
                        case 'right':
                            // Swipe from left center to right center
                            startXSlider.value = 30; // 0.3 relative
                            startYSlider.value = 50; // 0.5 relative
                            endXSlider.value = 70;   // 0.7 relative
                            endYSlider.value = 50;   // 0.5 relative
                            break;
                    }

                    // Update the displayed values
                    if (startXValueSpan) startXValueSpan.textContent = startXSlider.value;
                    if (startYValueSpan) startYValueSpan.textContent = startYSlider.value;
                    if (endXValueSpan) endXValueSpan.textContent = endXSlider.value;
                    if (endYValueSpan) endYValueSpan.textContent = endYSlider.value;

                    // Update the numeric input fields
                    if (startXInput) startXInput.value = startXSlider.value;
                    if (startYInput) startYInput.value = startYSlider.value;
                    if (endXInput) endXInput.value = endXSlider.value;
                    if (endYInput) endYInput.value = endYSlider.value;
                }
            });
        }

        // Setup Save and Load recording buttons
        if (this.saveRecordingBtn) {
            this.saveRecordingBtn.addEventListener('click', async () => {
                // Direct save implementation to avoid any side effects
                if (!this.currentTestCaseName || !this.currentTestCaseFilename) {
                    // If no current test case name, treat as Save As
                    this.saveRecordingAs();
                    return;
                }

                console.log(`Directly saving test case: ${this.currentTestCaseName}, filename: ${this.currentTestCaseFilename}`);
                console.log(`Current actions:`, this.currentActions);

                // Show saving notification
                this.showToast(`Saving test case "${this.currentTestCaseName}"...`, 'info', 1500);
                this.showLoading();
                this.logAction('info', `Saving test case "${this.currentTestCaseName}"...`);

                try {
                    // Log actions for debugging
                    console.log(`Saving test case with ${this.currentActions.length} actions`);
                    for (let i = 0; i < this.currentActions.length; i++) {
                        const action = this.currentActions[i];
                        if (action.fallback_locators && action.fallback_locators.length > 0) {
                            console.log(`Action ${i+1} has ${action.fallback_locators.length} fallback locators`);
                            console.log('Fallback locators:', JSON.stringify(action.fallback_locators));
                        }
                    }

                    // Direct API call
                    const result = await this.fetchApi('recording/save', 'POST', {
                        name: this.currentTestCaseName,
                        filename: this.currentTestCaseFilename,
                        currentActions: this.currentActions,
                        isSaveAs: false
                    });

                    this.hideLoading();

                    if (result.status === 'saved') {
                        this.logAction('success', `Test case ${this.currentTestCaseName} saved successfully`);
                        // Add a more visible notification
                        this.showToast(`Test case "${this.currentTestCaseName}" saved successfully`, 'success', 3000);
                    } else {
                        this.logAction('error', result.error || 'Failed to save test case');
                        this.showToast(`Error: ${result.error || 'Failed to save test case'}`, 'danger', 3000);
                    }
                } catch (error) {
                    this.hideLoading();
                    this.logAction('error', `Save error: ${error.message}`);
                    console.error('Save error:', error);
                    this.showToast(`Save error: ${error.message}`, 'danger', 3000);
                }
            });
        }

        if (this.saveAsRecordingBtn) {
            this.saveAsRecordingBtn.addEventListener('click', () => this.saveRecordingAs());
        }

        if (this.loadRecordingBtn) {
            this.loadRecordingBtn.addEventListener('click', () => this.loadRecording());
        }

        // Setup Execute actions button
        // if (this.executeActionsBtn) {
        //     this.executeActionsBtn.addEventListener('click', () => this.executeActions());
        // }
        // ^^^ Commented out: Listener is now handled by ExecutionManager in execution-manager.js

        // // Test Cases tab buttons // <-- Start comment/removal here
        // if (this.refreshTestCasesBtn) {
        //     this.refreshTestCasesBtn.addEventListener('click', () => this.loadAllTestCases());
        // }
        //
        // if (this.testCaseSearchInput) {
        //     this.testCaseSearchInput.addEventListener('input', () => this.filterTestCases());
        // }
        //
        // if (this.clearTestCaseSearchBtn) {
        //     this.clearTestCaseSearchBtn.addEventListener('click', () => {
        //         this.testCaseSearchInput.value = '';
        //         this.filterTestCases();
        //     });
        // }
        //
        // if (this.sortByNameBtn) {
        //     this.sortByNameBtn.addEventListener('click', () => {
        //         this.testCasesSort = 'name';
        //         this.sortByNameBtn.classList.add('active');
        //         this.sortByDateBtn.classList.remove('active');
        //         this.displayTestCases();
        //     });
        // }
        //
        // if (this.sortByDateBtn) {
        //     this.sortByDateBtn.addEventListener('click', () => {
        //         this.testCasesSort = 'date';
        //         this.sortByDateBtn.classList.add('active');
        //         this.sortByNameBtn.classList.remove('active');
        //         this.displayTestCases();
        //     });
        // }
        //
        // // Setup test-cases-tab activation event // Also remove/comment this listener
        // document.getElementById('test-cases-tab-btn').addEventListener('click', () => {
        //     // Load test cases when tab is activated
        //     this.loadAllTestCases();
        // }); // <-- End comment/removal here

        // Listen for Execute Single Action button (if it exists)
        if (this.executeSingleActionBtn) {
            this.executeSingleActionBtn.addEventListener('click', () => this.executeSingleAction());
        }

        // Set up element inspector action buttons
        const addElementTapAction = document.getElementById('addElementTapAction');
        if (addElementTapAction) {
            addElementTapAction.addEventListener('click', () => this.addTapActionFromElement());
        }

        const addElementSwipeAction = document.getElementById('addElementSwipeAction');
        if (addElementSwipeAction) {
            addElementSwipeAction.addEventListener('click', () => this.addSwipeActionFromElement());
        }

        const addElementTextAction = document.getElementById('addElementTextAction');
        if (addElementTextAction) {
            addElementTextAction.addEventListener('click', () => this.addTextActionFromElement());
        }

        // Add event listener for Add Action button
        if (this.addActionBtn) {
            this.addActionBtn.addEventListener('click', () => this.actionManager.addAction());
        }

        // Add event listener for Clear Actions button
        if (this.clearActionsBtn) {
            this.clearActionsBtn.addEventListener('click', () => this.clearActions());
        }

        // Settings tab event listeners
        if (this.browseTestCasesBtn) {
            this.browseTestCasesBtn.addEventListener('click', () => {
                this.testCasesDirPicker.click();
            });
        }

        if (this.browseReportsBtn) {
            this.browseReportsBtn.addEventListener('click', () => {
                this.reportsDirPicker.click();
            });
        }

        if (this.testCasesDirPicker) {
            this.testCasesDirPicker.addEventListener('change', (event) => {
                if (event.target.files.length > 0) {
                    // Get directory path from the first file
                    const filePath = event.target.files[0].path;
                    const dirPath = filePath.substring(0, filePath.lastIndexOf('/'));
                    this.testCasesDirInput.value = dirPath;
                }
            });
        }

        if (this.reportsDirPicker) {
            this.reportsDirPicker.addEventListener('change', (event) => {
                if (event.target.files.length > 0) {
                    // Get directory path from the first file
                    const filePath = event.target.files[0].path;
                    const dirPath = filePath.substring(0, filePath.lastIndexOf('/'));
                    this.reportsDirInput.value = dirPath;
                }
            });
        }

        if (this.resetSettingsBtn) {
            this.resetSettingsBtn.addEventListener('click', () => {
                this.resetSettings();
            });
        }

        if (this.saveSettingsBtn) {
            this.saveSettingsBtn.addEventListener('click', () => {
                this.saveSettings();
            });
        }

        // Settings tab activation
        const settingsTabLink = document.querySelector('button#settings-tab-btn'); // Use the correct ID selector for the button
        if(settingsTabLink) { // Check if the element was found
             settingsTabLink.addEventListener('click', () => {
                 this.setupSettingsTab(); // setupSettingsTab might also access elements, ensure they exist there too if needed
             });
        } else {
             // Optionally log an error if the element is crucial and not found
             console.error("Settings tab button ('button#settings-tab-btn') not found during event listener setup."); // Updated error message
        }



        // Add event listeners for the If-Else form elements
        const ifConditionType = document.getElementById('ifConditionType');
        if (ifConditionType) {
            ifConditionType.addEventListener('change', () => this.actionFormManager.handleIfConditionTypeChange());
        }

        const ifExistsLocatorType = document.getElementById('ifExistsLocatorType');
        if (ifExistsLocatorType) {
            ifExistsLocatorType.addEventListener('change', () => this.actionFormManager.handleIfExistsLocatorTypeChange());
        }

        // Add event listener for Wait Till Element locator type change
        const waitTillLocatorType = document.getElementById('waitTillLocatorType');
        if (waitTillLocatorType) {
            waitTillLocatorType.addEventListener('change', () => this.actionFormManager.handleWaitTillLocatorTypeChange());
        }

        const thenActionType = document.getElementById('thenActionType');
        if (thenActionType) {
            thenActionType.addEventListener('change', () => this.actionFormManager.handleThenActionTypeChange());
        }

        const elseActionType = document.getElementById('elseActionType');
        if (elseActionType) {
            elseActionType.addEventListener('change', () => this.actionFormManager.handleElseActionTypeChange());
        }

        // Setup all event listeners for the application
        const dropdownActionTypes = document.getElementById('actionType');
        
        // Tap Image Text Input Toggle
        const tapImageUseText = document.getElementById('tapImageUseText');
        if (tapImageUseText) {
            tapImageUseText.addEventListener('change', (e) => {
                const isChecked = e.target.checked;
                const textInputGroup = document.getElementById('tapImageTextInputGroup');
                const dropdownGroup = document.getElementById('tapImageFilename').parentElement;
                
                textInputGroup.style.display = isChecked ? 'flex' : 'none';
                document.getElementById('tapImageFilename').disabled = isChecked;
                
                // Hide validation messages when toggling
                document.getElementById('tapImageValidationSuccess').style.display = 'none';
                document.getElementById('tapImageValidationError').style.display = 'none';
            });
        }
        
        // Tap Image Validation
        const validateTapImage = document.getElementById('validateTapImage');
        if (validateTapImage) {
            validateTapImage.addEventListener('click', async () => {
                const imageName = document.getElementById('tapImageTextInput').value.trim();
                
                if (!imageName) {
                    this.showValidationMessage('tapImageValidationError', 'Please enter an image name.', true);
                    return;
                }
                
                try {
                    const response = await fetch('/api/reference_images/validate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ image_name: imageName })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok && data.status === 'success') {
                        document.getElementById('tapImageValidationSuccess').style.display = 'block';
                        document.getElementById('tapImageValidationError').style.display = 'none';
                    } else {
                        this.showValidationMessage('tapImageValidationError', data.message || 'Image not found.', true);
                    }
                } catch (error) {
                    console.error('Error validating image:', error);
                    this.showValidationMessage('tapImageValidationError', 'Error validating image.', true);
                }
            });
        }
        
        // Double Tap Image Text Input Toggle
        const doubleTapImageUseText = document.getElementById('doubleTapImageUseText');
        if (doubleTapImageUseText) {
            doubleTapImageUseText.addEventListener('change', (e) => {
                const isChecked = e.target.checked;
                const textInputGroup = document.getElementById('doubleTapImageTextInputGroup');
                const dropdownGroup = document.getElementById('doubleTapImageFilename').parentElement;
                
                textInputGroup.style.display = isChecked ? 'flex' : 'none';
                document.getElementById('doubleTapImageFilename').disabled = isChecked;
                
                // Hide validation messages when toggling
                document.getElementById('doubleTapImageValidationSuccess').style.display = 'none';
                document.getElementById('doubleTapImageValidationError').style.display = 'none';
            });
        }
        
        // Double Tap Image Validation
        const validateDoubleTapImage = document.getElementById('validateDoubleTapImage');
        if (validateDoubleTapImage) {
            validateDoubleTapImage.addEventListener('click', async () => {
                const imageName = document.getElementById('doubleTapImageTextInput').value.trim();
                
                if (!imageName) {
                    this.showValidationMessage('doubleTapImageValidationError', 'Please enter an image name.', true);
                    return;
                }
                
                try {
                    const response = await fetch('/api/reference_images/validate', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({ image_name: imageName })
                    });
                    
                    const data = await response.json();
                    
                    if (response.ok && data.status === 'success') {
                        document.getElementById('doubleTapImageValidationSuccess').style.display = 'block';
                        document.getElementById('doubleTapImageValidationError').style.display = 'none';
                    } else {
                        this.showValidationMessage('doubleTapImageValidationError', data.message || 'Image not found.', true);
                    }
                } catch (error) {
                    console.error('Error validating image:', error);
                    this.showValidationMessage('doubleTapImageValidationError', 'Error validating image.', true);
                }
            });
        }
    }

    // Setup Save and Load buttons
    setupSaveLoadButtons() {
        // Get fresh references to the buttons
        const saveRecordingBtn = document.getElementById('saveRecordingBtn');
        const loadRecordingBtn = document.getElementById('loadRecordingBtn');

        console.log('Setting up Save/Load buttons:', {
            saveBtn: saveRecordingBtn,
            loadBtn: loadRecordingBtn
        });

        // Add event listener for Load Recording button
        if (loadRecordingBtn) {
            // Remove any existing click listeners to prevent duplicates
            loadRecordingBtn.replaceWith(loadRecordingBtn.cloneNode(true));

            // Get fresh reference after clone
            const freshLoadBtn = document.getElementById('loadRecordingBtn');

            // Add proper click event listener
            freshLoadBtn.addEventListener('click', () => {
                console.log('Load button clicked');
                this.loadRecording();
            });

            console.log('Load button event listener added');
        } else {
            console.error('Load button not found in DOM');
        }

        // We now use the data-bs-toggle attribute directly on the save button for opening the modal
        // Just set up the test case name input and confirm button handlers
        const testCaseNameInput = document.getElementById('testCaseName');
        const confirmSaveButton = document.getElementById('confirmSaveTestCase');
        const saveTestCaseModal = document.getElementById('saveTestCaseModal');

        if (testCaseNameInput && confirmSaveButton && saveTestCaseModal) {
            // Clear and focus on input when the modal is shown
            saveTestCaseModal.addEventListener('shown.bs.modal', () => {
                console.log('Save modal shown');
                testCaseNameInput.value = ''; // Clear the input field
                testCaseNameInput.focus();
            });

            // Add keyboard support for Enter key
            testCaseNameInput.addEventListener('keypress', (event) => {
                if (event.key === 'Enter') {
                    confirmSaveButton.click();
                }
            });

            // Set up the confirm save button functionality
            confirmSaveButton.onclick = async () => {
                console.log('Save button clicked');
                await this.saveTestCase(testCaseNameInput.value.trim());
            };
        } else {
            console.error('Save modal elements not found', {
                testCaseNameInput,
                confirmSaveButton,
                saveTestCaseModal
            });
        }
    }

    // New method to save test case
    async saveTestCase(testCaseName, isSaveAs = false) {
        if (!testCaseName) {
            // Show error if no name is provided
            this.logAction('error', 'Please enter a test case name');
            return;
        }

        // Get labels from the input field
        const labelsInput = document.getElementById('testCaseLabels');
        const labels = labelsInput && labelsInput.value ? 
            labelsInput.value.split(',').map(label => label.trim()).filter(label => label) : 
            [];

        // Hide the modal
        const saveTestCaseModal = document.getElementById('saveTestCaseModal');
        if (saveTestCaseModal) {
            bootstrap.Modal.getInstance(saveTestCaseModal).hide();
        }

        // Show loading
        this.showLoading();

        this.logAction('info', `Saving test case as "${testCaseName}"...`);
        try {
            const result = await this.fetchApi('recording/save', 'POST', {
                name: testCaseName,
                labels: labels,
                currentActions: this.currentActions, // Include current actions from UI
                isSaveAs: isSaveAs // Add flag to indicate if this is a Save As operation
            });

            // Hide loading
            this.hideLoading();

            if (result.status === 'saved') {
                this.logAction('success', `Test case saved as ${result.filename}`);
                // Update the current test case name if this is a direct save
                if (!isSaveAs) {
                    this.currentTestCaseName = testCaseName;
                }
            } else {
                this.logAction('error', result.error || 'Failed to save test case');
            }
        } catch (error) {
            this.hideLoading();
            this.logAction('error', `Save error: ${error.message}`);
            console.error('Save error:', error);
        }
    }

    // Method to handle Save As
    async saveRecordingAs() {
        const saveTestCaseModal = document.getElementById('saveTestCaseModal');
        const testCaseNameInput = document.getElementById('testCaseName');
        const confirmSaveButton = document.getElementById('confirmSaveTestCase');

        if (!saveTestCaseModal || !testCaseNameInput || !confirmSaveButton) {
            this.logAction('error', 'Unable to open save dialog - UI components not found');
            return;
        }

        // Initialize the modal - Use getOrCreateInstance for stability
        let modalInstance;
        try {
             modalInstance = bootstrap.Modal.getOrCreateInstance(saveTestCaseModal);
        } catch (e) {
             console.error("Error getting or creating Bootstrap modal instance for saveTestCaseModal:", e);
             this.logAction('error', 'Failed to initialize save dialog.');
             return;
        }

        // Clear and focus input when modal is shown
        saveTestCaseModal.addEventListener('shown.bs.modal', () => {
            testCaseNameInput.value = '';
            testCaseNameInput.focus();
        });

        // Handle modal hidden event to ensure the page remains interactive
        saveTestCaseModal.addEventListener('hidden.bs.modal', () => {
            console.log("Save modal hidden"); // Optional log
        }, { once: true }); // <--- Add this option

        // Set up the confirm save button functionality
        confirmSaveButton.onclick = async () => {
            const testCaseName = testCaseNameInput.value.trim();
            if (!testCaseName) {
                this.logAction('error', 'Please enter a test case name');
                return;
            }

            try {
                this.showLoading();

                const result = await this.fetchApi('recording/save', 'POST', {
                    name: testCaseName,
                    currentActions: this.currentActions,
                    isSaveAs: true
                });

                this.hideLoading();

                if (result.status === 'saved') {
                    this.logAction('success', `Test case saved as ${result.filename}`);
                    // Update current test case name for future saves
                    this.currentTestCaseName = testCaseName;
                    // Hide the modal
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                } else {
                    this.logAction('error', result.error || 'Failed to save test case');
                }
            } catch (error) {
                this.hideLoading();
                this.logAction('error', `Save error: ${error.message}`);
                console.error('Save error:', error);
            }
        };

        // Add keyboard support for Enter key
        testCaseNameInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                confirmSaveButton.click();
            }
        });

        // Show the modal
        try {
             if (modalInstance) {
                 modalInstance.show();
             } else {
                  throw new Error("Modal instance could not be obtained.");
             }
        } catch (e) {
             console.error("Error showing saveTestCaseModal:", e);
             this.logAction('error', 'Failed to show save dialog.');
        }
    }

    // API communication methods
    async fetchApi(endpoint, method = 'GET', body = null) {
        const url = `/api/${endpoint}`;
        const options = {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        if (body && (method === 'POST' || method === 'PUT')) {
            options.body = JSON.stringify(body);
        }

        return fetch(url, options)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! Status: ${response.status}`);
                }
                return response.json();
            });
    }

    // Device management methods
    async refreshDevices() {
        try {
            this.logAction('info', 'Refreshing device list...');
            const result = await this.fetchApi('devices');

            // Clear existing options
            this.deviceSelect.innerHTML = '<option value="">Select a device</option>';

            // Add devices to select
            if (result.devices && result.devices.length > 0) {
                result.devices.forEach(device => {
                    const option = document.createElement('option');
                    option.value = device.id;
                    option.textContent = `${device.model || 'Unknown'} (${device.id})`;
                    // *** ADDED: Set the data-platform attribute ***
                    option.dataset.platform = device.platform;
                    this.deviceSelect.appendChild(option);
                });

                this.logAction('success', `Found ${result.devices.length} device(s)`);
            } else {
                this.logAction('warning', 'No devices found');
            }
        } catch (error) {
            this.logAction('error', `Failed to refresh devices: ${error.message}`);
        }
    }

    async connectToDevice() {
        if (this.isConnected) {
            this.disconnectDevice();
            return;
        }

        const selectedOption = this.deviceSelect.options[this.deviceSelect.selectedIndex];
        const deviceId = selectedOption.value;
        // Get the platform from the data-platform attribute of the selected option
        const platform = selectedOption.dataset.platform;

        if (!deviceId) {
            this.showToast('Error', 'Please select a device first.', 'error');
            return;
        }

        // Log the platform being sent
        console.log(`Attempting to connect to device: ${deviceId}, platform: ${platform}`);
        this.logAction('info', `Connecting to device: ${deviceId} (Platform: ${platform})...`);
        this.showLoading(`Connecting to ${deviceId}...`);

        try {
            // Include both device_id and platform in the payload
            const result = await this.fetchApi('device/connect', 'POST', { device_id: deviceId, platform: platform });

            if (result && result.status === 'connected') {
                this.isConnected = true;
                this.deviceId = deviceId;
                this.connectBtn.textContent = 'Disconnect';
                this.connectBtn.classList.replace('btn-success', 'btn-danger');

                // Update UI state
                this.updateUIForConnectedState(true);

                // Update device screen
                if (result.screenshot) {
                    this.deviceScreen.src = result.screenshot;
                }

                // Update device information and trigger emulator maintenance if needed
                if (result.device_info) {
                    this.deviceInfo = result.device_info;
                    this.updateDeviceInfo(result.device_info);

                    // Log device dimensions if available in the connect response
                    if (result.device_info.width && result.device_info.height) {
                        console.log(`Device dimensions from connect response: ${result.device_info.width}x${result.device_info.height}`);
                    }
                }

                // Check if AirTest is available
                if (result.airtest_available) {
                    this.logAction('success', `Connected to device: ${deviceId} with AirTest support`);

                    // Update UI to show AirTest is available
                    const deviceHeader = document.querySelector('.card-header.bg-primary');
                    if (deviceHeader) {
                        const airTestBadge = document.createElement('span');
                        airTestBadge.id = 'airTestBadge';
                        airTestBadge.className = 'badge bg-success ms-2';
                        airTestBadge.innerHTML = '<i class="bi bi-wind"></i> AT';
                        deviceHeader.querySelector('h5').appendChild(airTestBadge);
                    }
                } else {
                    this.logAction('success', `Connected to device: ${deviceId}`);
                }

                // Store connected device ID in sessionStorage
                sessionStorage.setItem('connectedDeviceId', this.deviceId);

            } else {
                this.logAction('error', result.error || 'Failed to connect to device');
            }
        } catch (error) {
            this.hideLoading();
            this.logAction('error', `Connection error: ${error.message}`);
        } finally {
            // Hide loading overlay after attempt (success or failure)
            this.hideLoading();
        }
    }

    async disconnectDevice() {
        try {
            this.logAction('info', 'Disconnecting from device...');
            await this.fetchApi('device/disconnect', 'POST');

            // Reset connection state
            this.isConnected = false;
            this.deviceId = null;
            this.connectBtn.textContent = 'Connect';
            this.connectBtn.classList.replace('btn-danger', 'btn-success');

            // Update UI state
            this.updateUIForConnectedState(false);

            // Reset device screen
            this.deviceScreen.src = 'static/img/no_device.png';

            this.logAction('success', 'Disconnected from device');

            // Remove device ID from sessionStorage
            sessionStorage.removeItem('connectedDeviceId');

        } catch (error) {
            this.logAction('error', `Disconnection error: ${error.message}`);
        }
    }

    async refreshScreenshot() {
        if (!this.isConnected) return;

        try {
            this.logAction('info', 'Refreshing screenshot...');
            const result = await this.fetchApi('screenshot');

            if (result.status === 'success' && (result.screenshot || result.screenshot_url)) {
                // Add a timestamp to prevent caching
                const timestamp = new Date().getTime();
                let screenshotUrl = result.screenshot_url || result.screenshot;

                // Check if it's already a full URL or just a path
                if (!screenshotUrl.startsWith('http') && !screenshotUrl.startsWith('/')) {
                    screenshotUrl = '/' + screenshotUrl;
                }

                // Ensure screenshots are loaded from /static/screenshots/ instead of /screenshots/
                if (screenshotUrl.startsWith('/screenshots/')) {
                    screenshotUrl = '/static' + screenshotUrl;
                }

                // Add timestamp parameter to prevent caching
                screenshotUrl = screenshotUrl.includes('?')
                    ? `${screenshotUrl}&t=${timestamp}`
                    : `${screenshotUrl}?t=${timestamp}`;

                console.log(`Refreshing screenshot with URL: ${screenshotUrl}`);

                // Update the image source
                if (this.deviceScreen) {
                    // Add error handling for the image
                    this.deviceScreen.onerror = () => {
                        console.error('Failed to load screenshot from URL:', screenshotUrl);
                        this.logAction('error', 'Failed to refresh screenshot');

                        // Try again with a different URL after a short delay
                        setTimeout(() => {
                            const newTimestamp = new Date().getTime();
                            const fallbackUrl = `/screenshot?t=${newTimestamp}`;
                            console.log(`Trying fallback screenshot URL: ${fallbackUrl}`);
                            this.deviceScreen.src = fallbackUrl;
                        }, 1000);
                    };

                    // Add success handler
                    this.deviceScreen.onload = () => {
                        console.log('Screenshot loaded successfully');
                        this.logAction('success', 'Screenshot refreshed successfully');
                    };

                    this.deviceScreen.src = screenshotUrl;
                    this.logAction('success', 'Screenshot refreshed');
                } else {
                    this.logAction('error', 'Device screen element not found');
                }
            } else if (result.path) {
                // Some endpoints return path instead of screenshot or screenshot_url
                const screenshotUrl = `/static/screenshots/${result.path.split('/').pop()}?t=${new Date().getTime()}`;
                console.log(`Using path-based screenshot URL: ${screenshotUrl}`);
                this.refreshScreenshotFromUrl(screenshotUrl);
                this.logAction('success', 'Screenshot refreshed from path');
            } else {
                this.logAction('error', result.error || 'Failed to refresh screenshot');

                // Try to reconnect if screenshot refresh fails
                try {
                    this.logAction('info', 'Attempting to recover connection...');
                    const reconnectResult = await this.fetchApi('device/reconnect', 'POST');

                    // Check if reconnect returned a screenshot URL
                    if (reconnectResult.screenshot_url) {
                        console.log(`Using screenshot URL from reconnect: ${reconnectResult.screenshot_url}`);
                        this.refreshScreenshotFromUrl(reconnectResult.screenshot_url);
                    } else {
                        // Try again after reconnect
                        setTimeout(() => this.refreshDevice(), 1000);
                    }
                } catch (reconnectError) {
                    this.logAction('error', `Reconnect error: ${reconnectError.message}`);
                }
            }
        } catch (error) {
            this.logAction('error', `Screenshot refresh error: ${error.message}`);

            // Attempt recovery
            setTimeout(() => {
                this.logAction('info', 'Retrying screenshot refresh...');
                this.refreshDevice();
            }, 1000);
        }
    }

    // Method to refresh screenshot from a specific URL
    refreshScreenshotFromUrl(url) {
        if (!this.deviceScreen) {
            this.logAction('error', 'Device screen element not found');
            return;
        }

        // Check if URL is valid
        if (!url) {
            this.logAction('error', 'Invalid screenshot URL provided');
            console.error('Invalid screenshot URL:', url);
            // Fall back to regular screenshot refresh
            this.refreshScreenshot();
            return;
        }

        // Add a timestamp to prevent caching
        const timestamp = new Date().getTime();
        let screenshotUrl = url;

        // Check if it's already a full URL or just a path
        if (!screenshotUrl.startsWith('http') && !screenshotUrl.startsWith('/')) {
            screenshotUrl = '/' + screenshotUrl;
        }

        // Ensure screenshots are loaded from /static/screenshots/ instead of /screenshots/
        if (screenshotUrl.startsWith('/screenshots/')) {
            screenshotUrl = '/static' + screenshotUrl;
        }

        // Add timestamp parameter to prevent caching
        screenshotUrl = screenshotUrl.includes('?')
            ? `${screenshotUrl}&t=${timestamp}`
            : `${screenshotUrl}?t=${timestamp}`;

        console.log(`Refreshing screenshot with URL: ${screenshotUrl}`);

        // Update the image source
        this.deviceScreen.src = screenshotUrl;
        this.logAction('info', `Screenshot refreshed from URL: ${screenshotUrl}`);

        // Add error handling for the image
        this.deviceScreen.onerror = () => {
            console.error('Failed to load screenshot from URL:', screenshotUrl);
            this.logAction('error', 'Failed to load screenshot from provided URL');
            // Fall back to regular screenshot refresh
            this.refreshScreenshot();
        };
    }

    // Recording methods
    async startRecording() {
        if (!this.isConnected) {
            this.logAction('error', 'Cannot start recording - no device connected');
            return;
        }

        try {
            this.logAction('info', 'Starting recording...');
            const result = await this.fetchApi('recording/start', 'POST');

            if (result.status === 'recording_started') {
                this.isRecording = true;

                // Update UI
                if (this.recordBtn) this.recordBtn.disabled = true;
                if (this.stopRecordingBtn) this.stopRecordingBtn.disabled = false;

                this.logAction('success', 'Recording started');
            } else {
                this.logAction('error', result.error || 'Failed to start recording');
            }
        } catch (error) {
            this.logAction('error', `Recording error: ${error.message}`);
        }
    }

    async stopRecording() {
        if (!this.isRecording) {
            return;
        }

        try {
            this.logAction('info', 'Stopping recording...');
            const result = await this.fetchApi('recording/stop', 'POST');

            if (result.status === 'recording_stopped') {
                this.isRecording = false;

                // Update UI
                if (this.recordBtn) this.recordBtn.disabled = false;
                if (this.stopRecordingBtn) this.stopRecordingBtn.disabled = true;
                if (this.saveRecordingBtn) this.saveRecordingBtn.disabled = false;

                this.logAction('success', `Recording stopped with ${result.action_count} actions`);
            } else {
                this.logAction('error', result.error || 'Failed to stop recording');
            }
        } catch (error) {
            this.logAction('error', `Recording error: ${error.message}`);
        }
    }

    async saveRecording() {
        try {
            console.log('saveRecording method called');

            // Get fresh reference to the test case name input and confirm button
            const testCaseNameInput = document.getElementById('testCaseName');
            const confirmSaveButton = document.getElementById('confirmSaveTestCase');
            const saveTestCaseModal = document.getElementById('saveTestCaseModal');

            if (!testCaseNameInput || !confirmSaveButton || !saveTestCaseModal) {
                console.error('Save modal elements not found', { testCaseNameInput, confirmSaveButton });
                this.logAction('error', 'Unable to open save dialog - UI components not found');
                return;
            }

            // Clear and focus on input when the modal is shown
            saveTestCaseModal.addEventListener('shown.bs.modal', () => {
                console.log('Modal shown event triggered');
                testCaseNameInput.value = ''; // Clear the input field
                testCaseNameInput.focus();
            });

            // Add keyboard support for Enter key
            testCaseNameInput.addEventListener('keypress', (event) => {
                if (event.key === 'Enter') {
                    confirmSaveButton.click();
                }
            });

            // Set up event listener for the save button
            confirmSaveButton.onclick = async () => {
                console.log('Save button clicked');
                const testCaseName = testCaseNameInput.value.trim();

                if (!testCaseName) {
                    // Show error if no name is provided
                    this.logAction('error', 'Please enter a test case name');
                    return;
                }

                // Hide the modal
                bootstrap.Modal.getInstance(saveTestCaseModal).hide();

                // Show loading
                this.showLoading();

                this.logAction('info', `Saving test case as "${testCaseName}"...`);
                const result = await this.fetchApi('recording/save', 'POST', {
                    name: testCaseName,
                    currentActions: this.currentActions // Include current actions from UI
                });

                // Hide loading
                this.hideLoading();

                if (result.status === 'saved') {
                    this.logAction('success', `Test case saved as ${result.filename}`);
                } else {
                    this.logAction('error', result.error || 'Failed to save test case');
                }
            };
        } catch (error) {
            this.hideLoading();
            this.logAction('error', `Save error: ${error.message}`);
            console.error('Save error:', error);
        }
    }

    async reconnectDevice() {
        try {
            if (!this.isConnected || !this.deviceId) {
                console.error('No device connected to reconnect');
                this.showToast('No device connected. Please connect to a device first.', 'error');
                return false;
            }

            console.log(`Attempting to reconnect to device ${this.deviceId}`);
            this.showToast(`Reconnecting to device... please wait`, 'info', 2000);

            const result = await this.fetchApi('device/reconnect', 'POST');

            if (result.success) {
                console.log('Device reconnected successfully');
                this.showToast('Device reconnected successfully', 'success');
                return true;
            } else {
                console.error('Failed to reconnect:', result.error);
                this.showToast(`Failed to reconnect: ${result.error}`, 'error');
                return false;
            }
        } catch (error) {
            console.error('Error reconnecting to device:', error);
            this.showToast('Error reconnecting to device', 'error');
            return false;
        }
    }

    updateUIForExecuting(isExecuting, currentActionIndex = 0) {
        // Disable or enable buttons based on execution state
        this.executeActionsBtn.disabled = isExecuting;
        this.stopExecutionBtn.disabled = !isExecuting;

        // Visual indicators for executing actions
        const actionsList = document.getElementById('actionsList');
        if (!actionsList) return;

        // Reset all items
        const items = actionsList.querySelectorAll('.list-group-item');
        items.forEach(item => {
            item.classList.remove('executing', 'success', 'error');
        });

        // If executing, add an executing class to the current action
        if (isExecuting && items.length > 0 && currentActionIndex < items.length) {
            const currentItem = items[currentActionIndex];
            // Add executing style to current item
            currentItem.classList.add('executing');
        }
    }

    // Improved stopExecution method
    async stopExecution() {
        try {
            this.logAction('info', 'Stopping execution...');

            // Set a flag to indicate execution should stop
            this.stopExecutionRequested = true;

            // Call the backend to stop any ongoing operations
            const result = await this.fetchApi('action/stop', 'POST');

            // Reset UI state
            this.isExecuting = false;
            this.updateUIForExecuting(false);

            this.logAction('warning', 'Execution stopped by user');

            // Enable execute button and disable stop button
            if (this.executeActionsBtn) this.executeActionsBtn.disabled = false;
            if (this.stopExecutionBtn) this.stopExecutionBtn.disabled = true;

            // Hide loading spinner if it's shown
            this.hideLoading();

            // Show toast notification
            this.showToast('Execution stopped by user', 'warning');

            return true;
        } catch (error) {
            this.logAction('error', `Stop execution error: ${error.message}`);
            return false;
        }
    }

    // UI interaction methods
    async handleScreenClick(event) { // Make function async
        if (!this.isConnected) return;

        // Get click coordinates relative to the image ELEMENT
        const rect = this.deviceScreen.getBoundingClientRect();
        const clickXRelative = event.clientX - rect.left;
        const clickYRelative = event.clientY - rect.top;

        let deviceWidth, deviceHeight;

        // 1. Try to get dimensions from cached deviceInfo
        if (this.deviceInfo &&
            ((this.deviceInfo.width && this.deviceInfo.height) ||
             (this.deviceInfo.dimensions && this.deviceInfo.dimensions.width && this.deviceInfo.dimensions.height))) {

            // Handle dimensions stored directly in deviceInfo or in a dimensions object
            if (this.deviceInfo.dimensions) {
                deviceWidth = this.deviceInfo.dimensions.width;
                deviceHeight = this.deviceInfo.dimensions.height;
            } else {
                deviceWidth = this.deviceInfo.width;
                deviceHeight = this.deviceInfo.height;
            }
            console.log(`Using cached device dimensions: ${deviceWidth}x${deviceHeight}`);
        } else {
            // 2. Fallback: Fetch dimensions from the API
            try {
                console.log('Device dimensions not cached, fetching from /api/device/dimensions...');
                const dimensionsResult = await this.fetchApi('device/dimensions');
                if (dimensionsResult.success && dimensionsResult.dimensions) {
                    deviceWidth = dimensionsResult.dimensions.width;
                    deviceHeight = dimensionsResult.dimensions.height;
                    // Cache them for next time
                    this.deviceInfo = this.deviceInfo || {};
                    this.deviceInfo.width = deviceWidth;
                    this.deviceInfo.height = deviceHeight;
                    console.log(`Fetched device dimensions: ${deviceWidth}x${deviceHeight}`);
                } else {
                    this.logAction('error', 'Failed to get device dimensions from API. Using image natural dimensions as fallback.');
                    // Fallback to image natural dimensions if API fails (less reliable)
                    deviceWidth = this.deviceScreen.naturalWidth;
                    deviceHeight = this.deviceScreen.naturalHeight;
                }
            } catch (error) {
                this.logAction('error', `Error fetching device dimensions: ${error.message}. Using image natural dimensions as fallback.`);
                // Fallback on error
                deviceWidth = this.deviceScreen.naturalWidth;
                deviceHeight = this.deviceScreen.naturalHeight;
            }
        }

        // Calculate scaling factors based on the actual device dimensions vs image element size
        const scaleX = deviceWidth / rect.width;
        const scaleY = deviceHeight / rect.height;

        // Calculate absolute coordinates on the DEVICE screen
        const x = Math.round(clickXRelative * scaleX);
        const y = Math.round(clickYRelative * scaleY);

        console.log(`Calculated device coordinates: (${x}, ${y}) from relative click (${clickXRelative.toFixed(2)}, ${clickYRelative.toFixed(2)}) with scale (${scaleX.toFixed(2)}, ${scaleY.toFixed(2)})`);

        // Check if in inspect mode
        if (this.isInspectMode) {
            this.inspectElement(x, y); // Use calculated device coordinates
            return;
        }

        // Check if in picking mode
        if (this.pickingMode === 'tap') {
            // Fill tap coordinates in form
            document.getElementById('tapX').value = x; // Use calculated device coordinates
            document.getElementById('tapY').value = y; // Use calculated device coordinates

            // Exit picking mode
            this.pickingMode = null;
            this.deviceScreen.style.cursor = 'default';

            this.logAction('info', `Selected tap coordinates: (${x}, ${y})`);
            return;
        } else if (this.pickingMode === 'doubleClick') {
            // Fill double click coordinates in form
            document.getElementById('doubleClickX').value = x; // Use calculated device coordinates
            document.getElementById('doubleClickY').value = y; // Use calculated device coordinates

            // Exit picking mode
            this.pickingMode = null;
            this.deviceScreen.style.cursor = 'default';

            this.logAction('info', `Selected double click coordinates: (${x}, ${y})`);
            return;
        } else if (this.pickingMode === 'doubleTap') {
            // Fill double tap coordinates in form
            document.getElementById('doubleTapX').value = x;
            document.getElementById('doubleTapY').value = y;

            // Disable picking mode
            this.disablePickMode();

            this.logAction('info', `Selected double tap coordinates: (${x}, ${y})`);
            return;
        } else if (this.pickingMode === 'tapAndType') {
            // Fill tap and type coordinates in form
            document.getElementById('tapAndTypeX').value = x;
            document.getElementById('tapAndTypeY').value = y;

            // Reset picking mode
            this.pickingMode = null;
            this.deviceScreen.style.cursor = 'default';

            // Reset the button
            const pickBtn = document.getElementById('pickTapAndTypeCoordinates');
            if (pickBtn) {
                pickBtn.textContent = 'Pick from Screen';
                pickBtn.classList.replace('btn-outline-danger', 'btn-outline-primary');

                // Remove any cancel handler and restore original
                const newBtn = pickBtn.cloneNode(true);
                pickBtn.parentNode.replaceChild(newBtn, pickBtn);

                // Add the original event listener back
                newBtn.addEventListener('click', () => {
                    if (!this.isConnected) {
                        this.logAction('error', 'Cannot pick coordinates - no device connected');
                        return;
                    }

                    this.logAction('info', 'Click on the device screen to select coordinates for Tap and Type action');
                    this.pickingMode = 'tapAndType';

                    if (this.deviceScreen) {
                        this.deviceScreen.style.cursor = 'crosshair';
                    }

                    newBtn.textContent = 'Cancel Picking';
                    newBtn.classList.replace('btn-outline-primary', 'btn-outline-danger');
                });
            }

            this.logAction('info', `Selected Tap and Type coordinates: (${x}, ${y})`);
            return;
        }

        // If not in picking mode, execute tap action
        this.elementInteractions.executeTap(x, y);
    }

    // Helper methods
    logAction(type, message) {
        // Create log entry
        const entry = document.createElement('div');
        entry.className = `log-entry log-${type}`;

        // Add timestamp
        const now = new Date();
        const time = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;

        // Set icon based on type
        let icon = '';
        switch (type) {
            case 'info':
                icon = '<i class="bi bi-info-circle"></i>';
                break;
            case 'success':
                icon = '<i class="bi bi-check-circle"></i>';
                break;
            case 'warning':
                icon = '<i class="bi bi-exclamation-triangle"></i>';
                break;
            case 'error':
                icon = '<i class="bi bi-x-circle"></i>';
                break;
            default:
                icon = '<i class="bi bi-dash-circle"></i>';
                break;
        }

        entry.innerHTML = `
            <span class="log-time">[${time}]</span>
            <span class="log-icon">${icon}</span>
            <span class="log-message">${message}</span>
        `;

        // Add to log container at the top
        if (this.actionLog) {
            this.actionLog.insertBefore(entry, this.actionLog.firstChild);
            // No need to scroll - new entries are at the top
        } else {
            // If element not found, log to console
            console.log(`[${type}] ${message}`);
        }

        // Also log to console for debugging
        console.log(`[${type}] ${message}`);
    }


    // Start image capture mode
    startImageCapture(actionType) {
        // Only allow if connected to device
        if (!this.isConnected) {
            this.logAction('error', 'Cannot capture image: No device connected');
            return;
        }

        // Check if we have a valid screenshot
        const deviceScreen = document.getElementById('deviceScreen');
        if (!deviceScreen || !deviceScreen.complete || !deviceScreen.naturalWidth) {
            this.logAction('error', 'Cannot capture image: No valid screenshot available');
            return;
        }

        this.logAction('info', 'Starting image capture mode. Select an area on the screen.');

        // Disable other interactions
        this.disableInteractions();

        // Set the capture mode
        this.imageCaptureMode = actionType;

        // Create overlay for selection
        const deviceScreenContainer = document.getElementById('deviceScreenContainer');
        const overlay = document.createElement('div');
        overlay.id = 'captureOverlay';
        overlay.style.position = 'absolute';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.width = '100%';
        overlay.style.height = '100%';
        overlay.style.cursor = 'crosshair';
        overlay.style.zIndex = '999';

        // No overlay instructions on the device screen
        deviceScreenContainer.appendChild(overlay);

        // Create selection box
        const selectionBox = document.createElement('div');
        selectionBox.id = 'selectionBox';
        selectionBox.style.position = 'absolute';
        selectionBox.style.border = '2px dashed red';
        selectionBox.style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
        selectionBox.style.display = 'none';
        selectionBox.style.zIndex = '1000';
        overlay.appendChild(selectionBox);

        // Create rectangle for selection
        const selectionRect = document.createElement('div');
        selectionRect.id = 'selectionRect';
        selectionRect.style.position = 'absolute';
        selectionRect.style.border = '2px dashed red';
        selectionRect.style.backgroundColor = 'rgba(255, 0, 0, 0.2)';
        selectionRect.style.display = 'none';
        overlay.appendChild(selectionRect);

        // Variables for tracking selection
        let isSelecting = false;
        let startX, startY, currentWidth, currentHeight;

        // Add event listeners for selection
        overlay.addEventListener('mousedown', (e) => {
            // Start selection
            isSelecting = true;

            // Get position relative to overlay
            const rect = overlay.getBoundingClientRect();
            startX = e.clientX - rect.left;
            startY = e.clientY - rect.top;

            // Show selection box at start position
            selectionBox.style.left = `${startX}px`;
            selectionBox.style.top = `${startY}px`;
            selectionBox.style.width = '0';
            selectionBox.style.height = '0';
            selectionBox.style.display = 'block';
        });

        overlay.addEventListener('mousemove', (e) => {
            if (!isSelecting) return;

            // Get current position relative to overlay
            const rect = overlay.getBoundingClientRect();
            const currentX = e.clientX - rect.left;
            const currentY = e.clientY - rect.top;

            // Calculate width and height
            const width = Math.abs(currentX - startX);
            const height = Math.abs(currentY - startY);

            // Calculate top-left position
            const left = Math.min(startX, currentX);
            const top = Math.min(startY, currentY);

            // Update selection box
            selectionBox.style.left = `${left}px`;
            selectionBox.style.top = `${top}px`;
            selectionBox.style.width = `${width}px`;
            selectionBox.style.height = `${height}px`;

            // Store current dimensions for use in mouseup
            currentWidth = width;
            currentHeight = height;
        });

        overlay.addEventListener('mouseup', async (e) => {
            if (!isSelecting) return;
            isSelecting = false;

            // Get final position relative to overlay
            const overlayRect = overlay.getBoundingClientRect();
            const endX = e.clientX - overlayRect.left;
            const endY = e.clientY - overlayRect.top;

            // Calculate final width and height relative to displayed image
            const selectionWidth = Math.abs(endX - startX);
            const selectionHeight = Math.abs(endY - startY);

            // Calculate top-left position relative to displayed image
            const selectionX = Math.min(startX, endX);
            const selectionY = Math.min(startY, endY);

            // Minimum size check
            if (selectionWidth < 10 || selectionHeight < 10) {
                this.logAction('warning', 'Selection too small. Please select a larger area.');
                this.cleanUpCaptureOverlay(); // Clean up overlay
                this.enableInteractions();
                return;
            }

            // Get the displayed screenshot element
            const deviceScreen = document.getElementById('deviceScreen');
            if (!deviceScreen || !deviceScreen.complete || !deviceScreen.naturalWidth) {
                 this.logAction('error', 'Cannot capture image: Screenshot element not found or not loaded.');
                 this.cleanUpCaptureOverlay();
                 this.enableInteractions();
                 return;
            }

            // Get displayed and natural dimensions
            const displayedWidth = deviceScreen.clientWidth;
            const displayedHeight = deviceScreen.clientHeight;
            const naturalWidth = deviceScreen.naturalWidth;
            const naturalHeight = deviceScreen.naturalHeight;

            // Log the selection details relative to displayed image
            this.logAction('info', `Selected area on displayed image: (${selectionX.toFixed(2)}, ${selectionY.toFixed(2)}) size ${selectionWidth.toFixed(2)}x${selectionHeight.toFixed(2)}`);
            this.logAction('info', `Displayed image size: ${displayedWidth}x${displayedHeight}, Natural image size: ${naturalWidth}x${naturalHeight}`);


            // Remove overlay and instructions
            this.cleanUpCaptureOverlay();

            // Re-enable interactions
            this.enableInteractions();

            // Prompt for image name
            const imageName = prompt('Enter a name for this image:', `captured_image_${Date.now()}`);
            if (imageName === null || imageName.trim() === '') {
                this.logAction('info', 'Image capture cancelled');
                return;
            }

            // Ensure name ends with .png
            let finalImageName = imageName.trim();
            if (!finalImageName.toLowerCase().endsWith('.png')) {
                finalImageName += '.png';
            }


            // --- Call new Backend API ---
            try {
                this.showLoading('Capturing image area...');
                const response = await fetch('/api/capture_image_area', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        name: finalImageName,
                        selection_x: selectionX,
                        selection_y: selectionY,
                        selection_width: selectionWidth,
                        selection_height: selectionHeight,
                        displayed_width: displayedWidth,
                        displayed_height: displayedHeight,
                        natural_width: naturalWidth,
                        natural_height: naturalHeight,
                        save_debug: true // Optionally send debug flag
                    })
                });

                const result = await response.json();
                this.hideLoading();

                if (response.ok && result.status === 'success') {
                    this.logAction('success', `Image area captured and saved as ${finalImageName}`);
                    this.showImagePreview(finalImageName, result.image); // Show preview

                    // Update dropdowns (similar logic as before)
                    this.updateImageDropdown(this.imageCaptureMode, result.filename);

                } else {
                    this.logAction('error', `Failed to capture image area: ${result.error || 'Unknown error'}`);
                }

            } catch (error) {
                this.hideLoading();
                this.logAction('error', `Error calling capture API: ${error.message}`);
                console.error('Error calling capture API:', error);
            }
            // --- End calling new Backend API ---

        }); // End mouseup listener

        // Add ESC key handler to cancel
        const escHandler = (e) => {
            if (e.key === 'Escape') {
                this.cleanUpCaptureOverlay();
                this.enableInteractions();
                document.removeEventListener('keydown', escHandler);
                this.logAction('info', 'Image capture cancelled');
            }
        };
        document.addEventListener('keydown', escHandler);
    }

    // Helper to clean up capture overlay elements
    cleanUpCaptureOverlay() {
        const overlay = document.getElementById('captureOverlay');
        if (overlay) overlay.remove();
    }

    // Helper to update image dropdown after capture
    updateImageDropdown(actionType, filename) {
        let selectElementId;
        let loadType;

                if (actionType === 'tap') {
            selectElementId = 'tapImageFilename';
            loadType = 'tap';
                } else if (actionType === 'doubleTap') {
            selectElementId = 'doubleTapImageFilename';
            loadType = 'doubleTap';
        } else if (actionType === 'tapIfImageExists') {
            selectElementId = 'tapIfImageExistsFilename';
            loadType = 'tap'; // Changed to match the implementation in the event listener
        } else if (actionType === 'clickImageAirtest') { // Match existing type if used
             selectElementId = 'airtestImageFilename';
             loadType = 'clickImageAirtest';
        } else if (actionType === 'waitImageAirtest') {
             selectElementId = 'waitAirtestImageFilename';
             loadType = 'waitImageAirtest';
        } else if (actionType === 'doubleClickImageAirtest') {
             selectElementId = 'doubleClickAirtestImageFilename';
             loadType = 'doubleClickImageAirtest';
        } else if (actionType === 'waitTill') {
             selectElementId = 'waitTillImage';
             loadType = 'waitTill';
                } else if (actionType === 'screenContains') {
            selectElementId = 'ifScreenContainsImage';
            loadType = 'ifScreenContains';
        } else {
            return; // Unknown action type
        }

        const selectElement = document.getElementById(selectElementId);
        if (selectElement) {
            this.loadReferenceImages(loadType); // Refresh the list
            // Select the newly captured image after a short delay for refresh
            setTimeout(() => {
                if (Array.from(selectElement.options).some(opt => opt.value === filename)) {
                    selectElement.value = filename;
                    this.logAction('info', `Selected newly captured image '${filename}' in dropdown.`);
            } else {
                    this.logAction('warning', `Could not find '${filename}' in dropdown after refresh.`);
                }
            }, 500);
        }
    }

    // Show image preview in a modal popup
    showImagePreview(name, imageData) {
        // Create a modal popup for the image preview
        const previewContainer = document.createElement('div');
        previewContainer.className = 'image-preview-container';
        previewContainer.style.position = 'fixed';
        previewContainer.style.top = '0';
        previewContainer.style.left = '0';
        previewContainer.style.width = '100%';
        previewContainer.style.height = '100%';
        previewContainer.style.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        previewContainer.style.display = 'flex';
        previewContainer.style.justifyContent = 'center';
        previewContainer.style.alignItems = 'center';
        previewContainer.style.zIndex = '2000';

        // Create the modal content
        previewContainer.innerHTML = `
            <div class="image-preview-content" style="background-color: white; padding: 20px; border-radius: 8px; max-width: 80%; max-height: 80%; overflow: auto; text-align: center;">
                <h4>Image Captured Successfully</h4>
                <p>Name: ${name}</p>
                <div style="margin: 15px 0; border: 1px solid #ddd; padding: 10px;">
                    <img src="${imageData}" alt="Captured Image" style="max-width: 100%; max-height: 60vh;">
                </div>
                <div>
                    <p class="text-muted">This image has been saved to your reference images and selected in the dropdown.</p>
                    <button class="btn btn-primary" id="closePreviewBtn">Close</button>
                </div>
            </div>
        `;

        document.body.appendChild(previewContainer);

        // Add event listener to close button
        document.getElementById('closePreviewBtn').addEventListener('click', () => {
            previewContainer.remove();
        });

        // Also close when clicking outside the modal content
        previewContainer.addEventListener('click', (e) => {
            if (e.target === previewContainer) {
                previewContainer.remove();
            }
        });

        // Add keyboard event to close with Escape key
        const handleKeyDown = (e) => {
            if (e.key === 'Escape') {
                previewContainer.remove();
                document.removeEventListener('keydown', handleKeyDown);
            }
        };
        document.addEventListener('keydown', handleKeyDown);
    }

    clearActionLog() {
        if (this.actionLog) {
            this.actionLog.innerHTML = '';
            this.logAction('info', 'Action log cleared');
        }
    }

    toggleInspectElement() {
        if (this.elementInteractions) {
            this.elementInteractions.toggleInspectElement();
        } else {
            this.logAction('error', 'ElementInteractions module not loaded');
        }
    }

    async inspectElement(x, y) {
        return this.elementInteractions.inspectElement(x, y);
    }

    async executeTap(x, y) {
        return this.elementInteractions.executeTap(x, y);
    }

    setupElementActionButtons() {
        this.elementInteractions.setupElementActionButtons();
    }

    addTapActionFromElement() {
        this.elementInteractions.addTapActionFromElement();
    }

    addClearActionFromElement() {
        this.elementInteractions.addClearActionFromElement();
    }

    addInputTextActionFromElement() {
        this.elementInteractions.addInputTextActionFromElement();
    }

    addLongTapActionFromElement() {
        this.elementInteractions.addLongTapActionFromElement();
    }

    getElementDescription(element) {
        return this.elementInteractions.getElementDescription(element);
    }

    addTextActionFromElement() {
        this.elementInteractions.addTextActionFromElement();
    }

    updateStepNumbers() {
        // Update all step numbers after reordering or deletion
        const actionsList = document.getElementById('actionsList');
        if (actionsList) {
            const items = Array.from(actionsList.children);
            items.forEach((item, index) => {
                // Update the item's data-action-index
                item.dataset.actionIndex = index.toString();

                // Update the step number badge if it exists
                const stepBadge = item.querySelector('.step-number');
                if (stepBadge) {
                    stepBadge.textContent = (index + 1).toString();
                }
            });
        }
    }


    updateExecutionButtons() {
        // Enable or disable execution buttons based on whether there are actions
        const hasActions = Array.isArray(this.currentActions) && this.currentActions.length > 0;
        console.log(`[AppiumAutomationApp updateExecutionButtons Instance: ${this.instanceId}] Checking actions array: `, {length: this.currentActions.length, isArray: Array.isArray(this.currentActions), actions: JSON.stringify(this.currentActions)}); // MODIFIED LOG

        // Also check if there are action items in the DOM as a fallback
        const actionsList = document.getElementById('actionsList');
        const hasActionItems = actionsList && actionsList.children.length > 0;

        // Use either condition to determine if we have actions
        const shouldEnableButtons = hasActions || hasActionItems;

        if (this.executeActionsBtn) {
            this.executeActionsBtn.disabled = !shouldEnableButtons || !this.isConnected;
        }

        if (document.getElementById('clearActions')) {
            document.getElementById('clearActions').disabled = !shouldEnableButtons;
        }

        // Enable/disable save buttons - only require actions, not connection
        if (this.saveRecordingBtn) {
            this.saveRecordingBtn.disabled = !shouldEnableButtons;
            console.log(`Save button disabled: ${this.saveRecordingBtn.disabled}`);
        }

        if (this.saveAsRecordingBtn) {
            this.saveAsRecordingBtn.disabled = !shouldEnableButtons;
            console.log(`Save As button disabled: ${this.saveAsRecordingBtn.disabled}`);
        }

        console.log(`Updated execution buttons. Has actions: ${hasActions}, Has action items: ${hasActionItems}, Is connected: ${this.isConnected}`);
    }

    async loadReferenceImages(actionType, targetElementId = null) {
        console.log(`Loading reference images for action type: ${actionType}, target: ${targetElementId || 'general'}`);

        try {
            // Get the image select field based on action type or use the provided target element ID
            let imageSelect;

            if (targetElementId) {
                // If a specific target element ID is provided, use that
                imageSelect = document.getElementById(targetElementId);
            } else if (actionType === 'clickImage') {
                imageSelect = document.getElementById('clickImagePath');
            } else if (actionType === 'doubleClickImage') {
                imageSelect = document.getElementById('doubleClickImagePath');
            } else if (actionType === 'waitImage') {
                imageSelect = document.getElementById('waitImagePath');
            } else if (actionType === 'existsImage') {
                imageSelect = document.getElementById('existsImagePath');
            } else if (actionType === 'exists') {
                imageSelect = document.getElementById('existsImage');
            } else if (actionType === 'clickImageAirtest') {
                imageSelect = document.getElementById('airtestImageFilename');
            } else if (actionType === 'waitImageAirtest') {
                imageSelect = document.getElementById('waitAirtestImageFilename');
            } else if (actionType === 'doubleClickImageAirtest') {
                imageSelect = document.getElementById('doubleClickAirtestImageFilename');
            } else if (actionType === 'waitTill') {
                imageSelect = document.getElementById('waitTillImage');
            } else if (actionType === 'tap') {
                imageSelect = document.getElementById('tapImageFilename');
            } else if (actionType === 'doubleTap') {
                imageSelect = document.getElementById('doubleTapImageFilename');
            } else if (actionType === 'swipeTillVisible') {
                imageSelect = document.getElementById('swipeTillVisibleReferenceImage');
            } else if (actionType === 'ifExists') {
                imageSelect = document.getElementById('ifExistsImage');
            } else if (actionType === 'ifScreenContains') {
                imageSelect = document.getElementById('ifScreenContainsImage');
            } else {
                console.warn(`Image select field not found for ${actionType}`);
                return;
            }

            if (!imageSelect) {
                console.warn(`Image select field not found for ${actionType}${targetElementId ? ` with ID ${targetElementId}` : ''}`);
                return;
            }

            // Clear the current options
            imageSelect.innerHTML = '<option value="">Select an image...</option>';

            // Fetch the list of reference images
            const response = await fetch('/api/reference_images');
            if (!response.ok) {
                console.error('Error fetching reference images:', response.statusText);
                return;
            }

            const data = await response.json();
            console.log('Reference images:', data);

            if (data.status === 'success' && data.images) {
                // Add each image to the select
                data.images.forEach(image => {
                    const option = document.createElement('option');
                    // Handle both string and object formats
                    if (typeof image === 'string') {
                        option.value = image;
                        option.textContent = image;
                    } else if (typeof image === 'object' && image !== null) {
                        // If it's an object with path property
                        if (image.path) {
                            option.value = image.path;
                            option.textContent = image.path;
                        } else {
                            // Fallback to stringifying the object
                            console.warn('Image object missing path property:', image);
                            option.value = JSON.stringify(image);
                            option.textContent = image.toString();
                        }
                    }
                    imageSelect.appendChild(option);
                });

                console.log(`Added ${data.images.length} reference images to select`);
            } else {
                console.warn('No reference images found or error in response');
                const option = document.createElement('option');
                option.value = "";
                option.textContent = "No reference images available";
                imageSelect.appendChild(option);
            }
        } catch (error) {
            console.error('Error loading reference images:', error);
        }
    }

    enablePickMode(mode) {
        if (!this.isConnected) {
            this.logAction('error', 'Cannot pick coordinates - no device connected');
            return;
        }

        // Set global picking mode
        this.pickingMode = mode;

        // Change cursor style
        this.deviceScreen.style.cursor = 'crosshair';

        if (mode === 'tap') {
            this.logAction('info', 'Click on the screen to pick tap coordinates');
        } else if (mode === 'swipe') {
            this.pickingStartPoint = null;
            this.logAction('info', 'Click and drag on the screen to draw swipe path');

            // Add mouse down/move/up handlers for swipe
            this.deviceScreen.addEventListener('mousedown', this.handlePickSwipeStart.bind(this));
            document.addEventListener('mousemove', this.handlePickSwipeMove.bind(this));
            document.addEventListener('mouseup', this.handlePickSwipeEnd.bind(this));
        }
    }

    handlePickSwipeStart(event) {
        if (this.pickingMode !== 'swipe') return;

        // Get start coordinates relative to the image
        const rect = this.deviceScreen.getBoundingClientRect();
        const scaleX = this.deviceScreen.naturalWidth / rect.width;
        const scaleY = this.deviceScreen.naturalHeight / rect.height;

        const x = Math.round((event.clientX - rect.left) * scaleX);
        const y = Math.round((event.clientY - rect.top) * scaleY);

        this.pickingStartPoint = { x, y };

        // Fill swipe start coordinates in form
        document.getElementById('swipeStartX').value = x;
        document.getElementById('swipeStartY').value = y;
    }

    handlePickSwipeMove(event) {
        if (this.pickingMode !== 'swipe' || !this.pickingStartPoint) return;

        // Show visual feedback of swipe (could be implemented with an overlay canvas)
    }

    handlePickSwipeEnd(event) {
        if (this.pickingMode !== 'swipe' || !this.pickingStartPoint) return;

        // Get end coordinates relative to the image
        const rect = this.deviceScreen.getBoundingClientRect();
        const scaleX = this.deviceScreen.naturalWidth / rect.width;
        const scaleY = this.deviceScreen.naturalHeight / rect.height;

        const x = Math.round((event.clientX - rect.left) * scaleX);
        const y = Math.round((event.clientY - rect.top) * scaleY);

        // Fill swipe end coordinates in form
        document.getElementById('swipeEndX').value = x;
        document.getElementById('swipeEndY').value = y;

        // Exit picking mode
        this.pickingMode = null;
        this.deviceScreen.style.cursor = 'default';

        // Remove event listeners
        this.deviceScreen.removeEventListener('mousedown', this.handlePickSwipeStart.bind(this));
        document.removeEventListener('mousemove', this.handlePickSwipeMove.bind(this));
        document.removeEventListener('mouseup', this.handlePickSwipeEnd.bind(this));

        this.logAction('info', `Selected swipe from (${this.pickingStartPoint.x}, ${this.pickingStartPoint.y}) to (${x}, ${y})`);

        // Reset start point
        this.pickingStartPoint = null;
    }

    async loadRecording() {
        console.log("Starting test suite loading");

        try {
            // Fetch test suites instead of test cases
            const response = await fetch('/api/test_suites/list', {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) {
                alert("Failed to load test suites. Server returned: " + response.status);
                return;
            }

            const data = await response.json();
            console.log("Got test suite data:", data);

            // Log each test suite's test cases for debugging
            if (data.test_suites && data.test_suites.length > 0) {
                data.test_suites.forEach(suite => {
                    console.log(`Test Suite: ${suite.name}, Test Cases:`, suite.test_cases);
                });
            }

            // Get the modal element
            const modal = document.getElementById('loadTestCaseModal');
            if (!modal) {
                alert("Could not find the load test suite modal");
                return;
            }

            // Get the modal body and test suites list
            const modalBody = modal.querySelector('.modal-body');
            if (!modalBody) {
                alert("Could not find the modal body");
                return;
            }

            // Clear previous content to prevent duplicates
            modalBody.innerHTML = '';

            // Create HTML for test suites
            let listHtml = '';

            if (data.test_suites && data.test_suites.length > 0) {
                for (const suite of data.test_suites) {
                    const formattedDate = new Date(suite.created).toLocaleDateString();
                    const testCasesCount = suite.test_cases.length;

                    // Create the main suite item
                    listHtml += `
                        <div class="test-suite-item">
                            <div class="test-suite-info">
                                <h4 class="test-suite-name">${suite.name}</h4>
                                <div class="test-suite-meta">
                                    Created: ${formattedDate} · ${testCasesCount} Test Case${testCasesCount !== 1 ? 's' : ''}
                                </div>
                            </div>
                            <div class="test-suite-action">
                                <button type="button" class="btn btn-primary"
                                        data-suite-id="${suite.id}" data-name="${suite.name}">
                                    <i class="bi bi-check-lg"></i> Select
                                </button>
                            </div>
                        </div>
                    `;

                    // Start creating test cases section
                    listHtml += `<div class="collapse mt-2" id="suite-${suite.id}">
                        <div class="test-case-dropdown">`;

                    // Add placeholder for test cases that will be populated later
                    listHtml += `<div id="test-cases-${suite.id}" class="test-cases-container">
                        <div class="text-center p-3">
                            <div class="spinner-border spinner-border-sm text-primary" role="status">
                                <span class="visually-hidden">Loading test cases...</span>
                            </div>
                            <span class="ms-2">Loading test cases...</span>
                        </div>
                    </div>`;

                    listHtml += `</div></div>
                        <button class="btn btn-sm btn-outline-secondary mb-3" type="button" data-bs-toggle="collapse"
                                data-bs-target="#suite-${suite.id}" aria-expanded="false">
                            <i class="bi bi-chevron-down"></i> Show Test Cases
                        </button>
                    `;
                }
            } else {
                // No test suites found
                listHtml = `
                    <div class="text-center p-5">
                        <i class="bi bi-exclamation-circle text-muted fs-1"></i>
                        <p class="mt-2">No test suites found</p>
                    </div>
                `;
            }

            // Replace the modal body content
            modalBody.innerHTML = listHtml;

            // Add CSS to hide undefined text
            const style = document.createElement('style');
            style.textContent = '.test-case-name:after { content: none !important; }';
            document.head.appendChild(style);

            // Now fetch and populate test case names
            if (data.test_suites && data.test_suites.length > 0) {
                for (const suite of data.test_suites) {
                    const container = document.getElementById(`test-cases-${suite.id}`);
                    if (!container) continue;

                    // Create HTML for test cases with actual names
                    let testCasesHtml = '';

                    try {
                        // Fetch all test case names first
                        const testCasePromises = suite.test_cases.map(async (testCase) => {
                            // Extract the filename
                            const filename = typeof testCase === 'string' ? testCase :
                                            (testCase.filename ? testCase.filename : null);

                            if (!filename) return null;

                            // For simple display, just extract the base name without extension and timestamp
                            const simpleName = filename.split('_')[0].replace(/\.json$/, '');

                            try {
                                // Try to fetch the actual name, but don't fail if it doesn't work
                                const name = await this.fetchTestCaseName(filename);
                                return { filename, name };
                            } catch (err) {
                                // If fetch fails, use a simplified version of the filename
                                return { filename, name: simpleName || filename };
                            }
                        });

                        // Wait for all names to be fetched, with a timeout
                        const testCaseDetailsPromise = Promise.all(testCasePromises);

                        // Add a timeout for the overall operation
                        const timeoutPromise = new Promise((resolve) => {
                            setTimeout(() => {
                                resolve(suite.test_cases.map(tc => {
                                    const fn = typeof tc === 'string' ? tc : (tc.filename || '');
                                    const simpleName = fn.split('_')[0].replace(/\.json$/, '');
                                    return { filename: fn, name: simpleName || fn };
                                }));
                            }, 3000); // 3 second timeout
                        });

                        // Use the first promise to resolve
                        const testCaseDetails = await Promise.race([testCaseDetailsPromise, timeoutPromise]);

                        // Create HTML for each test case
                        testCaseDetails.forEach(testCase => {
                            if (!testCase) return;

                            testCasesHtml += `
                                <div class="list-item">
                                    <span class="test-case-name">${testCase.name}</span>
                                </div>
                            `;
                        });

                        // Update the container
                        container.innerHTML = testCasesHtml ||
                            '<div class="text-center p-2 text-muted">No test cases found</div>';

                    } catch (error) {
                        console.error(`Error fetching test case names for suite ${suite.id}:`, error);

                        // Simple fallback display without API calls
                        const fallbackHtml = suite.test_cases.map(tc => {
                            const fn = typeof tc === 'string' ? tc : (tc.filename || '');
                            const simpleName = fn.split('_')[0].replace(/\.json$/, '');
                            return `
                                <div class="list-item">
                                    <span class="test-case-name">${simpleName || fn}</span>
                                </div>
                            `;
                        }).join('');

                        container.innerHTML = fallbackHtml ||
                            '<div class="text-center p-2 text-danger">Error loading test cases</div>';
                    }
                }
            }

            // Initialize or get existing modal instance
            let bsModal = bootstrap.Modal.getInstance(modal);
            if (!bsModal) {
                bsModal = new bootstrap.Modal(modal, {
                    backdrop: true,
                    keyboard: true,
                    focus: true
                });
            }

            // Add click handlers to the test suite items
            modal.querySelectorAll('.btn-primary[data-suite-id]').forEach(item => {
                item.addEventListener('click', event => {
                    const suiteId = event.currentTarget.dataset.suiteId;
                    const name = event.currentTarget.dataset.name;

                    console.log(`Selected test suite: ${name} (${suiteId})`);

                    // Hide the modal properly
                    bsModal.hide();

                    // Load the selected test suite
                    this.loadSelectedTestSuite(suiteId);
                });
            });

            // Add a click handler to the Cancel button
            const cancelBtn = document.getElementById('loadTestCaseCancelBtn');
            if (cancelBtn) {
                const newCancelBtn = cancelBtn.cloneNode(true);
                cancelBtn.parentNode.replaceChild(newCancelBtn, cancelBtn);

                newCancelBtn.addEventListener('click', () => {
                    bsModal.hide();
                });
            }

            // Show the modal
            bsModal.show();
        } catch (error) {
            console.error("Error loading test suites:", error);
            alert("Error loading test suites: " + error.message);

            // Ensure modal cleanup even on error
            document.body.classList.remove('modal-open');
            const backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(backdrop => backdrop.remove());
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }
    }

    async loadSelectedTestSuite(suiteId) {
        if (this.isLoadingSuite) { // <-- Check the flag
            console.warn("Already loading a suite, ignoring request for:", suiteId);
            return;
        }
        this.isLoadingSuite = true; // <-- Set the flag

        try {
            this.showLoading('Loading test suite...');

            // Start fresh - clear any existing actions
            this.clearActions(); // <-- Clear actions after setting flag

            // Fetch the test suite details
            const response = await fetch(`/api/test_suites/${suiteId}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });
            if (!response.ok) {
                throw new Error(`Failed to load test suite: ${response.status}`);
            }

            const data = await response.json();
            if (data.status !== 'success') {
                throw new Error(data.error || 'Failed to load test suite');
            }

            const suite = data.test_suite;
            console.log('Test suite details:', suite);

            // Get the action list element
            const actionsList = document.getElementById('actionsList');
            if (!actionsList) {
                throw new Error('Action list element not found');
            }

            // Double-check that actionsList is empty
            if (actionsList.children.length > 0) {
                console.warn('Action list not empty after clearing, forcing clear');
                actionsList.innerHTML = '';
            }

            // Switch to Device Control tab
            document.getElementById('device-tab').click();

            // Keep track of both filenames and test case names to ensure no duplicates
            const processedFiles = new Set();
            const processedNames = new Set();

            // Create a processed list of unique test cases
            const uniqueTestCases = [];
            if (suite.test_cases && Array.isArray(suite.test_cases)) {
                for (const testCase of suite.test_cases) {
                    const filename = typeof testCase === 'string' ? testCase :
                                    (testCase.filename ? testCase.filename : null);

                    if (!filename) {
                        console.error('Invalid test case format:', testCase);
                        continue;
                    }

                    // Check if we've already processed this filename
                    if (!processedFiles.has(filename)) {
                        processedFiles.add(filename);
                        uniqueTestCases.push({ filename });
                    } else {
                        console.log(`Skipping duplicate test case filename: ${filename}`);
                    }
                }
            }

            console.log(`Processing ${uniqueTestCases.length} unique test cases of ${suite.test_cases.length} total`);

            const finalTestCases = [];

            // First pass: load all test cases and filter out duplicates by name
            for (let i = 0; i < uniqueTestCases.length; i++) {
                const { filename } = uniqueTestCases[i];

                try {
                    console.log(`Loading test case with filename: ${filename}`);

                    const testCaseResponse = await fetch(`/api/test_cases/load/${filename}`);
                    if (!testCaseResponse.ok) {
                        console.error(`Failed to load test case ${filename}`);
                        continue;
                    }

                    const testCaseResult = await testCaseResponse.json();
                    console.log(`Loaded test case data for ${filename}:`, testCaseResult);

                    if (testCaseResult.status === 'success' && testCaseResult.test_case) {
                        const loadedTestCase = testCaseResult.test_case;

                        // Get the test case name (either from name property or filename)
                        const testCaseName = loadedTestCase.name || filename;

                        // Check for duplicate names
                        if (!processedNames.has(testCaseName)) {
                            processedNames.add(testCaseName);
                            finalTestCases.push({
                                filename,
                                testCase: loadedTestCase
                            });
                        } else {
                            console.log(`Skipping test case with duplicate name: ${testCaseName}`);
                        }
                    }
                } catch (error) {
                    console.error(`Error loading test case ${filename}:`, error);
                }
            }

            console.log(`Adding ${finalTestCases.length} test cases after name deduplication`);

            // Now add the final filtered test cases to the UI
            for (let index = 0; index < finalTestCases.length; index++) {
                const { filename, testCase } = finalTestCases[index];
                try {
                    // Create a container for this test case
                    const testCaseContainer = document.createElement('div');
                    testCaseContainer.className = 'test-case-container mb-3';
                    testCaseContainer.dataset.filename = filename;
                    testCaseContainer.dataset.testCaseName = testCase.name || filename;
                    const testCaseId = `test-case-${filename.replace(/[^a-zA-Z0-9]/g, '-')}`;
                    testCaseContainer.dataset.testCaseId = testCaseId;
                    testCaseContainer.dataset.testIdx = index; // Add the test index as a data attribute
                    console.log(`Setting test case ${testCaseId} with index ${index}`);

                    // Add a header for this test case with expand/collapse functionality
                    const testCaseHeader = document.createElement('div');
                    testCaseHeader.className = 'list-group-item test-case-header bg-light d-flex justify-content-between align-items-center';
                    testCaseHeader.setAttribute('data-bs-toggle', 'collapse');
                    testCaseHeader.setAttribute('data-bs-target', `#${testCaseId}`);
                    testCaseHeader.setAttribute('aria-expanded', 'true'); // Start expanded
                    testCaseHeader.setAttribute('aria-controls', testCaseId);
                    testCaseHeader.style.cursor = 'pointer';

                    const actionCount = testCase.actions ? testCase.actions.length : 0;

                                        testCaseHeader.innerHTML = `
                        <div class="d-flex align-items-center">
                            <i class="bi bi-chevron-down me-2 collapse-icon" style="transition: transform 0.2s"></i>
                            <h6 class="mb-0">
                                <i class="bi bi-file-earmark-text me-2"></i>
                                ${testCase.name || filename}
                            </h6>
                            ${testCase.labels && testCase.labels.length > 0 ? 
                                `<div class="ms-2">
                                    ${testCase.labels.map(label => 
                                        `<span class="badge bg-secondary me-1">${label}</span>`
                                    ).join('')}
                                </div>` : ''}
                        </div>
                        <div class="d-flex align-items-center">
                            <button class="btn btn-sm btn-outline-primary retry-test-case me-2" title="Retry this test case" data-filename="${filename}" data-test-case-name="${testCase.name || filename}">
                                <i class="bi bi-arrow-clockwise"></i> Retry
                            </button>
                            <button class="btn btn-sm btn-outline-danger remove-test-case me-2" title="Remove from action list">
                                <i class="bi bi-x-circle"></i> Remove
                            </button>
                            <span class="badge bg-primary">${actionCount} actions</span>
                        </div>
                    `;

                    // Create a container for the actions (collapsible)
                    const actionsContainer = document.createElement('div');
                    actionsContainer.id = testCaseId;
                    actionsContainer.className = 'collapse show test-case-actions'; // Start expanded

                    // Track the beginning index of this test case's actions in the global array
                    const startActionIndex = this.currentActions.length;

                    // Add all actions from this test case with correct numbering
                    if (testCase.actions && Array.isArray(testCase.actions)) {
                        testCase.actions.forEach((action, localIndex) => {
                            // Add the action to the current actions array
                            this.currentActions.push(action);

                            // Calculate the global action index
                            const globalActionIndex = this.currentActions.length - 1;

                            // Create action item
                            const actionItem = document.createElement('div');
                            actionItem.className = 'list-group-item d-flex justify-content-between align-items-center action-item';
                            actionItem.dataset.actionIndex = globalActionIndex.toString();
                            actionItem.dataset.localIndex = localIndex.toString();

                            // Get action description
                            const actionText = this.actionManager.getActionDescription(action);

                            // Add action content with numbering - use local index + 1 for display
                            actionItem.innerHTML = `
                                <div class="action-content">
                                    <i class="bi bi-grip-vertical drag-indicator me-2" style="cursor:grab"></i>
                                    <span class="badge bg-secondary step-number me-2">${localIndex + 1}</span>
                                    <span class="badge bg-primary me-2">${action.type}</span>
                                    ${actionText}
                                </div>
                                <div class="action-buttons">
                                    <button class="btn btn-sm btn-outline-primary play-action me-1" title="Play this action">
                                        <i class="bi bi-play-fill"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-secondary edit-action me-1" title="Edit this action">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger delete-action" title="Delete this action">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </div>
                            `;

                            // Set up action button event listeners
                            const playButton = actionItem.querySelector('.play-action');
                            if (playButton) {
                                playButton.addEventListener('click', (e) => {
                                    e.stopPropagation(); // Prevent collapse toggle
                                    this.playAction(action, globalActionIndex, actionItem);
                                });
                            }

                            const editButton = actionItem.querySelector('.edit-action');
                            if (editButton) {
                                editButton.addEventListener('click', (e) => {
                                    e.stopPropagation(); // Prevent collapse toggle
                                    this.editAction(action, globalActionIndex);
                                });
                            }

                            const deleteButton = actionItem.querySelector('.delete-action');
                            if (deleteButton) {
                                // Pass the specific actionsContainer to the handler
                                const currentActionsContainer = actionsContainer;
                                deleteButton.addEventListener('click', (e) => {
                                    e.stopPropagation(); // Prevent collapse toggle
                                    if (confirm('Delete this action?')) {
                                        // Use the correct globalActionIndex to splice the main array
                                        const indexToDelete = parseInt(actionItem.dataset.actionIndex);
                                        if (!isNaN(indexToDelete) && indexToDelete < this.currentActions.length) {
                                            this.currentActions.splice(indexToDelete, 1);
                                        } else {
                                            console.error(`Invalid index ${indexToDelete} for deletion.`);
                                            // Optionally re-sync UI if splice fails
                                        }

                                        actionItem.remove();
                                        this.logAction('info', 'Action removed');

                                        // Update step numbers for the *entire flat list* after deletion
                                        this.updateStepNumbers();

                                        // Update execution buttons state
                                        this.updateExecutionButtons();
                                    } else {
                                        console.error(`Error deleting action: Invalid index ${indexToDelete}`);
                                        this.logAction('error', `Failed to delete action: Invalid index`);
                                    }
                                });
                            }

                            actionsContainer.appendChild(actionItem);
                        });
                    }

                    // Add both header and container to the test case container
                    testCaseContainer.appendChild(testCaseHeader);
                    testCaseContainer.appendChild(actionsContainer);

                    // Add container to the actions list
                    actionsList.appendChild(testCaseContainer);

                    // Set up collapse state change to update icon
                    const collapseInstance = new bootstrap.Collapse(actionsContainer, { toggle: false });

                    // Add event listener for the remove button
                    const removeButton = testCaseHeader.querySelector('.remove-test-case');
                    if (removeButton) {
                        removeButton.addEventListener('click', (e) => {
                            e.stopPropagation(); // Prevent collapse toggle

                            // Get indices of all actions in this test case
                            const actionItems = actionsContainer.querySelectorAll('.action-item');
                            const actionIndices = [];
                            actionItems.forEach(item => {
                                const actionIndex = parseInt(item.dataset.actionIndex);
                                if (!isNaN(actionIndex)) {
                                    actionIndices.push(actionIndex);
                                }
                            });

                            // Sort indices in descending order to avoid shifting problems when removing
                            actionIndices.sort((a, b) => b - a);

                            // Remove actions from the current actions array
                            actionIndices.forEach(index => {
                                this.currentActions.splice(index, 1);
                            });

                            // Remove the test case container from the UI
                            testCaseContainer.remove();

                            // Update step numbers and execution buttons
                            this.updateStepNumbers();
                            this.updateExecutionButtons();

                            // Log the action
                            this.logAction('info', `Removed test case "${testCase.name || filename}" from action list (${actionIndices.length} actions)`);
                        });
                    }

                    testCaseHeader.addEventListener('click', () => {
                        collapseInstance.toggle();
                    });

                    actionsContainer.addEventListener('show.bs.collapse', () => {
                        testCaseHeader.querySelector('.collapse-icon').style.transform = 'rotate(0deg)';
                    });

                    actionsContainer.addEventListener('hide.bs.collapse', () => {
                        testCaseHeader.querySelector('.collapse-icon').style.transform = 'rotate(-90deg)';
                    });
                } catch (error) {
                    console.error(`Error creating UI for test case ${filename}:`, error);
                }
            }

            // Update execute buttons state
            this.updateExecutionButtons();

            // Show success message
            this.showToast(`Loaded test suite "${suite.name}" with ${this.currentActions.length} actions`, 'success', 3000);

            this.hideLoading();

        } catch (error) {
            console.error('Error loading test suite:', error);
            this.showToast(`Error loading test suite: ${error.message}`, 'danger', 3000);
            this.hideLoading();
        } finally {
             this.isLoadingSuite = false; // <-- Reset the flag in finally block
        }
    }

    // Method to load a test case by filename
    async loadSelectedTestCase(filename) {
        if (!filename) {
            alert("No filename provided");
                            return;
                        }

        try {
            console.log(`Loading test case: ${filename}`);
            this.showLoading("Loading test case...");

            // Load the test case
            const response = await fetch(`/api/test_cases/load/${filename}`, {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) {
                throw new Error(`Server returned status: ${response.status}`);
            }

            const result = await response.json();
            console.log("Test case load result:", result);

            if (result.status !== 'success' || !result.test_case) {
                throw new Error("Failed to load test case data");
            }

            const testCase = result.test_case;

            // Handle the test case
            if (testCase.device_id && testCase.device_id !== 'select') {
                            this.selectDeviceById(testCase.device_id);
                        }

                        // Clear current actions
                        this.currentActions = [];
                        const actionsList = document.getElementById('actionsList');
                        if (actionsList) {
                            actionsList.innerHTML = '';
                        }

                        // Add loaded actions
                        if (testCase.actions && Array.isArray(testCase.actions)) {
                            testCase.actions.forEach(action => {
                                this.addActionToList(action);
                            });

                            this.logAction('success', `Loaded test case with ${testCase.actions.length} actions`);

                // Save the current test case name and filename for direct saving
                this.currentTestCaseName = testCase.name;
                this.currentTestCaseFilename = filename;

                // Update UI
                            this.updateExecutionButtons();
                        } else {
                            this.logAction('warning', 'The test case contains no actions');
                        }

            this.hideLoading();

        } catch (error) {
            console.error("Error loading test case:", error);
                        this.hideLoading();
            this.logAction('error', `Error loading test case: ${error.message}`);
        }
    }

    // Helper to clean up a modal

    /**
     * Add an action to the list - this is a wrapper around actionManager.addActionToList
     * @param {Object} action - The action object to add
     */
    addActionToList(action) {
        if (this.actionManager && typeof this.actionManager.addActionToList === 'function') {
            this.actionManager.addActionToList(action);
        } else {
            console.error('Error: actionManager or addActionToList method not available');
            this.logAction('error', 'Failed to add action to list: Action manager not properly initialized');
        }
    }

    // Helper method to select a device by ID in the dropdown
    selectDeviceById(deviceId, shouldConnect = true) { // Add shouldConnect flag
        if (!deviceId || !this.deviceSelect) return;

        // Find the option with the matching device ID
        const options = Array.from(this.deviceSelect.options);
        for (let i = 0; i < options.length; i++) {
            const option = options[i];
            if (option.value === deviceId) {
                this.deviceSelect.selectedIndex = i;
                this.logAction('info', `Selected device: ${option.text}`);

                // If not already connected, connect to this device (unless shouldConnect is false)
                if (shouldConnect && !this.isConnected) {
                    this.connectToDevice();
                }
                return;
            }
        }

        this.logAction('warning', `Device ${deviceId} not found in available devices`);
    }

    // Add this method to handle clear actions button
    clearActions() {
        // Clear the actions array
        this.currentActions = [];
        console.log('Cleared currentActions array');

        // Clear the UI list
        const actionsList = document.getElementById('actionsList');
        if (actionsList) {
            actionsList.innerHTML = '';
            console.log('Cleared actionsList UI');
        }

        // Reset test case name and filename
        this.currentTestCaseName = null;
        this.currentTestCaseFilename = null;
        console.log('Reset test case name and filename');

        // Update execution buttons
        this.updateExecutionButtons();

        // Delete all screenshots
        this.deleteAllScreenshots();

                            // Log the action
        this.logAction('info', 'All actions cleared');
    }

    // Method to delete all screenshots
    async deleteAllScreenshots() {
        try {
            this.logAction('info', 'Cleaning up screenshots...');
            const response = await fetch('/api/screenshots/delete_all', {
                method: 'POST'
            });

            const result = await response.json();

            if (result.status === 'success') {
                this.logAction('success', 'All screenshots deleted successfully');
            } else {
                this.logAction('warning', `Failed to delete screenshots: ${result.message || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Error deleting screenshots:', error);
            this.logAction('warning', `Error deleting screenshots: ${error.message}`);
        }
    }

    async playAction(actionData, index, actionItemElement = null) {
        try {
            if (!this.isConnected) {
                this.logAction('error', 'Cannot play action - no device connected');
                return false; // Return false on failure
            }

            // Determine the target action item for UI updates
            let itemToUpdate = actionItemElement;
            if (!itemToUpdate) {
                const actionsList = document.getElementById('actionsList');
                if (actionsList && actionsList.children[index]) {
                    itemToUpdate = actionsList.children[index];
                }
            }

            // Mark this action as executing
            if (itemToUpdate) {
                // Add visual feedback
                itemToUpdate.classList.remove('success', 'error'); // Clear previous states
                itemToUpdate.classList.add('executing');
                
                // Remove existing icons before adding spinner
                const existingStatusIcon = itemToUpdate.querySelector('.action-content .bi-check-circle-fill, .action-content .bi-x-circle-fill');
                if (existingStatusIcon) existingStatusIcon.remove();

                const spinner = document.createElement('span');
                spinner.className = 'spinner-border spinner-border-sm me-2';
                spinner.setAttribute('role', 'status');

                // Add spinner to action content
                const actionContent = itemToUpdate.querySelector('.action-content');
                if (actionContent) {
                    actionContent.prepend(spinner);
                }
            }

            this.logAction('info', `Executing action: ${this.actionManager.getActionDescription(actionData)}`);

            // Show loading indicator
            this.showLoading('Executing action...');

            // Play the action through the API
            try {
                const response = await this.fetchApi('action/execute', 'POST', {
                    action: actionData,
                    force_screenshot: true  // Always get a fresh screenshot for individual actions
                });

                // If successful, refresh the screenshot
                if (response.success) {
                    // Explicitly call the refreshScreenshot method like the button does
                    this.refreshScreenshot();

                    this.logAction('success', `Action executed: ${response.message || this.actionManager.getActionDescription(actionData)}`);

                    // Update visual feedback
                    if (itemToUpdate) {
                        itemToUpdate.classList.remove('executing');
                        itemToUpdate.classList.add('success');

                        // Remove spinner
                        const spinner = itemToUpdate.querySelector('.spinner-border');
                        if (spinner) {
                            spinner.remove();
                        }

                        // Add success icon
                        const actionContent = itemToUpdate.querySelector('.action-content');
                        if (actionContent && !actionContent.querySelector('.bi-check-circle-fill')) {
                            const successIcon = document.createElement('i');
                            successIcon.className = 'bi bi-check-circle-fill text-success me-2';
                            actionContent.prepend(successIcon);

                            // Check if this is part of a retry operation
                            const isPartOfRetry = itemToUpdate.closest('.test-case-container')?.querySelector('.retry-test-case')?.disabled;
                            
                            // Only auto-remove success indicator if not part of a retry
                            if (!isPartOfRetry) {
                                // Remove success indicator after 2 seconds
                                setTimeout(() => {
                                    if (itemToUpdate.classList.contains('success')) { // Check if still success
                                        itemToUpdate.classList.remove('success');
                                        if (successIcon) {
                                            successIcon.remove();
                                        }
                                    }
                                }, 2000);
                            }
                        }
                    }
                    this.hideLoading();
                    return true; 
                } else {
                    this.logAction('error', `Failed to execute action: ${response.error || 'Unknown error'}`);

                    // Update visual feedback for error
                    if (itemToUpdate) {
                        itemToUpdate.classList.remove('executing');
                        itemToUpdate.classList.add('error');

                        // Remove spinner
                        const spinner = itemToUpdate.querySelector('.spinner-border');
                        if (spinner) {
                            spinner.remove();
                        }

                        // Add error icon
                        const actionContent = itemToUpdate.querySelector('.action-content');
                        if (actionContent && !actionContent.querySelector('.bi-x-circle-fill')) {
                            const errorIcon = document.createElement('i');
                            errorIcon.className = 'bi bi-x-circle-fill text-danger me-2';
                            actionContent.prepend(errorIcon);

                            // Check if this is part of a retry operation
                            const isPartOfRetry = itemToUpdate.closest('.test-case-container')?.querySelector('.retry-test-case')?.disabled;
                            
                            // Only auto-remove error indicator if not part of a retry
                            if (!isPartOfRetry) {
                                // Remove error indicator after 3 seconds
                                setTimeout(() => {
                                     if (itemToUpdate.classList.contains('error')) { // Check if still error
                                        itemToUpdate.classList.remove('error');
                                        if (errorIcon) {
                                            errorIcon.remove();
                                        }
                                    }
                                }, 3000);
                            }
                        }
                    }
                    this.hideLoading();
                    return false; 
                }
            } catch (error) {
                this.logAction('error', `Error executing action: ${error.message}`);

                // Update visual feedback for error
                if (itemToUpdate) {
                    itemToUpdate.classList.remove('executing');
                    itemToUpdate.classList.add('error'); // Ensure error class is added

                    // Remove spinner
                    const spinner = itemToUpdate.querySelector('.spinner-border');
                    if (spinner) {
                        spinner.remove();
                    }
                     // Add error icon if not present
                    const actionContent = itemToUpdate.querySelector('.action-content');
                    if (actionContent && !actionContent.querySelector('.bi-x-circle-fill')) {
                        const errorIcon = document.createElement('i');
                        errorIcon.className = 'bi bi-x-circle-fill text-danger me-2';
                        actionContent.prepend(errorIcon);
                        
                        // Check if this is part of a retry operation
                        const isPartOfRetry = itemToUpdate.closest('.test-case-container')?.querySelector('.retry-test-case')?.disabled;
                        
                        // Only auto-remove error indicator if not part of a retry
                        if (!isPartOfRetry) {
                            // Remove error indicator after 3 seconds
                            setTimeout(() => {
                                if (itemToUpdate.classList.contains('error')) { // Check if still error
                                   itemToUpdate.classList.remove('error');
                                   if (errorIcon) {
                                       errorIcon.remove();
                                   }
                               }
                            }, 3000);
                        }
                    }
                }
                this.hideLoading();
                return false; 
            }

        } catch (error) {
            this.hideLoading();
            this.logAction('error', `Error playing action: ${error.message}`);
            return false; 
        }
    }

    editAction(actionData, index) {
        // Load the action data into the action builder form
        const actionType = actionData.type;
        console.log(`Editing action at index: ${index} (${typeof index})`);

        // Validate index
        if (index < 0 || index >= this.currentActions.length) {
            console.error(`Invalid index for editing: ${index}`);
            this.logAction('error', `Invalid action index for editing: ${index}`);
            return;
        }

        // Set the action type in the dropdown
        if (this.actionTypeSelect) {
            this.actionTypeSelect.value = actionType;
            this.actionFormManager.updateActionForm(); // Changed this line
        }

        // Fill in the form fields based on action type
        switch (actionType) {
            case 'tap':
                if (actionData.method === 'coordinates') {
                    // Switch to coordinates tab
                    const coordsTab = document.getElementById('tap-coordinates-tab');
                    if (coordsTab) coordsTab.click();

                    // Set coordinate values
                    document.getElementById('tapX').value = actionData.x || 0;
                    document.getElementById('tapY').value = actionData.y || 0;
                }
                else if (actionData.method === 'image') {
                    // Switch to image tab
                    const imageTab = document.getElementById('tap-image-tab');
                    if (imageTab) imageTab.click();

                    const imageFilename = actionData.image_filename || '';
                    
                    // Check if it's a manually entered path (containing env[] or not in dropdown)
                    const selectElement = document.getElementById('tapImageFilename');
                    let foundInDropdown = false;
                    
                    if (selectElement) {
                        // Try to find in dropdown
                        for (let i = 0; i < selectElement.options.length; i++) {
                            if (selectElement.options[i].value === imageFilename) {
                                selectElement.selectedIndex = i;
                                foundInDropdown = true;
                                break;
                            }
                        }
                    }
                    
                    // If not found in dropdown or starts with env[], use text input
                    if (!foundInDropdown || imageFilename.startsWith('env[')) {
                        document.getElementById('tapImageUseText').checked = true;
                        document.getElementById('tapImageTextInputGroup').style.display = 'flex';
                        document.getElementById('tapImageTextInput').value = imageFilename;
                        document.getElementById('tapImageFilename').disabled = true;
                    } else {
                        document.getElementById('tapImageUseText').checked = false;
                        document.getElementById('tapImageTextInputGroup').style.display = 'none';
                        document.getElementById('tapImageFilename').disabled = false;
                        document.getElementById('tapImageFilename').value = imageFilename;
                    }
                    
                    // Reset validation messages
                    document.getElementById('tapImageValidationSuccess').style.display = 'none';
                    document.getElementById('tapImageValidationError').style.display = 'none';
                    
                    document.getElementById('tapThreshold').value = actionData.threshold || 0.7;
                    document.getElementById('tapTimeout').value = actionData.timeout || 20;
                }
                else {
                    // Switch to locator tab
                    const locatorTab = document.getElementById('tap-locator-tab');
                    if (locatorTab) locatorTab.click();

                    // Set locator values
                    document.getElementById('tapLocatorType').value = actionData.locator_type || 'id';
                    document.getElementById('tapLocatorValue').value = actionData.locator_value || '';
                    document.getElementById('tapLocatorTimeout').value = actionData.timeout || 15;
                    document.getElementById('tapLocatorInterval').value = actionData.interval || 0.5;

                    // Load fallback locators if available
                    if (this.fallbackLocatorsManager && actionData.fallback_locators) {
                        this.fallbackLocatorsManager.loadFallbackLocators('tap', actionData);
                    }

                    // Load fallback action if available
                    if (this.tapFallbackManager && actionData.fallback_type) {
                        // Create fallback data object
                        const fallbackData = {
                            fallback_type: actionData.fallback_type
                        };

                        // Add fallback data based on type
                        switch (actionData.fallback_type) {
                            case 'coordinates':
                                fallbackData.x = actionData.fallback_x;
                                fallbackData.y = actionData.fallback_y;
                                break;

                            case 'image':
                                fallbackData.image_filename = actionData.fallback_image_filename;
                                fallbackData.threshold = actionData.fallback_threshold;
                                break;

                            case 'text':
                                fallbackData.text = actionData.fallback_text;
                                break;

                            case 'locator':
                                fallbackData.locator_type = actionData.fallback_locator_type;
                                fallbackData.locator_value = actionData.fallback_locator_value;
                                break;
                        }

                        // Set fallback data in the manager
                        this.tapFallbackManager.fallbackData = fallbackData;
                        this.tapFallbackManager.fallbackActionAdded = true;

                        // Update UI
                        const addFallbackBtn = document.getElementById('addTapFallbackBtn');
                        if (addFallbackBtn) {
                            addFallbackBtn.textContent = 'Edit Fallback Action';
                            addFallbackBtn.classList.remove('btn-primary');
                            addFallbackBtn.classList.add('btn-success');
                        }

                        // Update fallback info
                        this.tapFallbackManager.updateFallbackInfo();
                    }
                }
                break;

            case 'swipe':
                document.getElementById('swipeStartX').value = actionData.start_x;
                document.getElementById('swipeStartY').value = actionData.start_y;
                document.getElementById('swipeEndX').value = actionData.end_x;
                document.getElementById('swipeEndY').value = actionData.end_y;
                document.getElementById('swipeDuration').value = actionData.duration || 300;
                document.getElementById('swipeCount').value = actionData.count || 1;
                document.getElementById('swipeInterval').value = actionData.interval || 0.5;

                // Update the numeric input fields
                document.getElementById('swipeStartXInput').value = actionData.start_x;
                document.getElementById('swipeStartYInput').value = actionData.start_y;
                document.getElementById('swipeEndXInput').value = actionData.end_x;
                document.getElementById('swipeEndYInput').value = actionData.end_y;

                // Update display spans
                document.getElementById('swipeStartXValue').textContent = actionData.start_x;
                document.getElementById('swipeStartYValue').textContent = actionData.start_y;
                document.getElementById('swipeEndXValue').textContent = actionData.end_x;
                document.getElementById('swipeEndYValue').textContent = actionData.end_y;

                // Handle swipe direction setting
                const swipeDirection = actionData.direction || 'custom';
                const swipeDirectionSelect = document.getElementById('swipeDirection');
                const swipeCustomSettings = document.getElementById('swipeCustomSettings');

                if (swipeDirectionSelect) {
                    swipeDirectionSelect.value = swipeDirection;

                    // Always show custom settings regardless of direction
                    // This allows users to see and adjust the coordinates for any direction
                }
                break;

            case 'text':
                document.getElementById('inputText').value = actionData.text;
                break;

            case 'sendKeys':
                document.getElementById('sendKeysLocatorType').value = actionData.locator_type || 'id';
                document.getElementById('sendKeysLocatorValue').value = actionData.locator_value || '';
                document.getElementById('sendKeysText').value = actionData.text || '';
                document.getElementById('sendKeysClearFirst').checked = actionData.clear_first !== false; // Default to true
                document.getElementById('sendKeysTimeout').value = actionData.timeout || 15;
                break;

            case 'key':
                document.getElementById('keyCode').value = actionData.key_code;
                break;

            case 'wait':
                document.getElementById('waitTime').value = actionData.duration;
                break;

            case 'launchApp':
                document.getElementById('appPackage').value = actionData.package;
                break;

            case 'terminateApp':
                document.getElementById('terminatePackage').value = actionData.package;
                break;

            case 'restartApp':
                document.getElementById('restartPackage').value = actionData.package;
                break;

            case 'uninstallApp':
                document.getElementById('uninstallPackage').value = actionData.package_id;
                break;

            case 'waitTill':
                // Set locator type
                if (actionData.locator_type) {
                    document.getElementById('waitTillLocatorType').value = actionData.locator_type;
                    // Trigger the change event handler to show/hide fields
                    this.handleWaitTillLocatorTypeChange();

                    if (actionData.locator_type === 'image') {
                        document.getElementById('waitTillImage').value = actionData.image;
                    } else {
                        document.getElementById('waitTillLocator').value = actionData.locator_value;
                    }
                } else {
                    // Handle legacy data format (before the change)
                    document.getElementById('waitTillLocatorType').value = 'image';
                    this.handleWaitTillLocatorTypeChange();
                    document.getElementById('waitTillImage').value = actionData.image;
                }

                document.getElementById('waitTillTimeout').value = actionData.timeout;
                document.getElementById('waitTillInterval').value = actionData.interval;
                break;

            case 'exists':
                document.getElementById('existsImage').value = actionData.image;
                break;

            case 'clickElement':
                document.getElementById('clickElementLocatorType').value = actionData.locator_type;
                document.getElementById('clickElementLocator').value = actionData.locator_value;
                document.getElementById('clickElementTimeout').value = actionData.timeout;
                break;

            case 'doubleClick':
                document.getElementById('doubleClickX').value = actionData.x;
                document.getElementById('doubleClickY').value = actionData.y;
                break;

            case 'textClear':
                document.getElementById('textClearInput').value = actionData.text;
                document.getElementById('textClearDelay').value = actionData.delay;
                break;
            case 'tapAndType':
                // Set the text and timeout values
                document.getElementById('tapAndTypeText').value = actionData.text || '';
                document.getElementById('tapAndTypeTimeout').value = actionData.timeout || 15;

                // Check which method to use
                if (actionData.method === 'coordinates') {
                    // Switch to coordinates tab
                    document.getElementById('tapAndType-coordinates-tab').click();

                    // Set coordinate values
                    document.getElementById('tapAndTypeX').value = actionData.x || 0;
                    document.getElementById('tapAndTypeY').value = actionData.y || 0;
                } else {
                    // Switch to locator tab
                    document.getElementById('tapAndType-locator-tab').click();

                    // Set locator values
                    document.getElementById('tapAndTypeLocatorType').value = actionData.locator_type || 'id';
                    document.getElementById('tapAndTypeLocatorValue').value = actionData.locator_value || '';
                }
                break;
            case 'clickImage': // <-- Add case for clickImage
                document.getElementById('ocrTextToFind').value = actionData.text_to_find || '';
                document.getElementById('ocrTimeout').value = actionData.timeout || 20;
                break;
            case 'clickImageAirtest': // Added case for Airtest
                document.getElementById('airtestImageFilename').value = actionData.image_filename || '';
                document.getElementById('airtestThreshold').value = actionData.threshold || 0.7;
                document.getElementById('airtestTimeout').value = actionData.timeout || 20;
                break;
            case 'waitImageAirtest': // Added case
                document.getElementById('waitAirtestImageFilename').value = actionData.image_filename || '';
                document.getElementById('waitAirtestThreshold').value = actionData.threshold || 0.7;
                document.getElementById('waitAirtestTimeout').value = actionData.timeout || 30;
                break;
            case 'doubleClickImageAirtest': // Added case
                document.getElementById('doubleClickAirtestImageFilename').value = actionData.image_filename || '';
                document.getElementById('doubleClickAirtestThreshold').value = actionData.threshold || 0.7;
                document.getElementById('doubleClickAirtestTimeout').value = actionData.timeout || 20;
                break;
            case 'tapAndType':
                // Set the text value
                document.getElementById('tapAndTypeText').value = actionData.text || '';
                document.getElementById('tapAndTypeTimeout').value = actionData.timeout || 15;

                // Check which method to use
                if (actionData.method === 'coordinates') {
                    // Switch to coordinates tab
                    document.getElementById('tapAndType-coordinates-tab').click();

                    // Set coordinate values
                    document.getElementById('tapAndTypeX').value = actionData.x || 0;
                    document.getElementById('tapAndTypeY').value = actionData.y || 0;
                } else {
                    // Switch to locator tab
                    document.getElementById('tapAndType-locator-tab').click();

                    // Set locator values
                    document.getElementById('tapAndTypeLocatorType').value = actionData.locator_type || 'id';
                    document.getElementById('tapAndTypeLocatorValue').value = actionData.locator_value || '';
                }
                break;

            case 'hideKeyboard':
                // Add any additional logic you want to execute when hideKeyboard is selected
                console.log('Hide keyboard action selected');
                break;

            case 'tapOnText':
                // Set the text to find and other parameters
                document.getElementById('tapOnTextToFind').value = actionData.text_to_find || '';
                document.getElementById('tapOnTextTimeout').value = actionData.timeout || 30;
                document.getElementById('tapOnTextDoubleTap').checked = actionData.double_tap === true;
                break;

            case 'addLog':
                // Set the log message and screenshot flag
                document.getElementById('addLogMessage').value = actionData.message || '';
                document.getElementById('addLogTakeScreenshot').checked = actionData.take_screenshot !== false; // Default to true
                break;

                        case 'iosFunctions':
                // Set the iOS function in the dropdown
                const iosFunctionSelect = document.getElementById('iosFunction');
                if (iosFunctionSelect && actionData.function_name) {
                    iosFunctionSelect.value = actionData.function_name;
                    
                    // Trigger the change handler to update the UI for this function
                    const changeEvent = new Event('change');
                    iosFunctionSelect.dispatchEvent(changeEvent);
                    
                    // Now fill in the function-specific parameters based on what fields were created by the change handler
                    setTimeout(() => {
                        switch (actionData.function_name) {
                            case 'press':
                                if (document.getElementById('iosKeyName')) {
                                    document.getElementById('iosKeyName').value = actionData.key || '';
                                } else if (document.getElementById('iosPressKey')) {
                                    document.getElementById('iosPressKey').value = actionData.key || '';
                                }
                                break;
                            case 'alert_click':
                                if (document.getElementById('iosAlertButton')) {
                                    document.getElementById('iosAlertButton').value = actionData.button_text || '';
                                } else if (document.getElementById('iosAlertButtonText')) {
                                    document.getElementById('iosAlertButtonText').value = actionData.button_text || '';
                                }
                                break;
                            case 'alert_wait':
                                if (document.getElementById('iosAlertTimeout')) {
                                    document.getElementById('iosAlertTimeout').value = actionData.timeout || 2;
                                }
                                break;
                            case 'set_clipboard':
                                if (document.getElementById('clipboardContent')) {
                                    document.getElementById('clipboardContent').value = actionData.content || '';
                                } else if (document.getElementById('iosClipboardContent')) {
                                    document.getElementById('iosClipboardContent').value = actionData.content || '';
                                }
                                break;
                            case 'text':
                                if (document.getElementById('iosTextInput')) {
                                    document.getElementById('iosTextInput').value = actionData.text || '';
                                    if (document.getElementById('iosTextEnter')) {
                                        document.getElementById('iosTextEnter').checked = actionData.enter !== false; // Default to true
                                    }
                                } else if (document.getElementById('iosTextContent')) {
                                    document.getElementById('iosTextContent').value = actionData.text || '';
                                    if (document.getElementById('iosTextEnter')) {
                                        document.getElementById('iosTextEnter').checked = actionData.enter !== false;
                                    }
                                }
                                break;
                            case 'push':
                                if (document.getElementById('iosPushSourcePath')) {
                                    document.getElementById('iosPushSourcePath').value = actionData.local_path || '';
                                } else if (document.getElementById('iosPushLocalPath')) {
                                    document.getElementById('iosPushLocalPath').value = actionData.local_path || '';
                                }
                                
                                if (document.getElementById('iosPushDestinationPath')) {
                                    document.getElementById('iosPushDestinationPath').value = actionData.remote_path || '';
                                } else if (document.getElementById('iosPushRemotePath')) {
                                    document.getElementById('iosPushRemotePath').value = actionData.remote_path || '';
                                }
                                break;
                            case 'clear_app':
                                if (document.getElementById('iosClearAppBundleID')) {
                                    document.getElementById('iosClearAppBundleID').value = actionData.bundle_id || '';
                                } else if (document.getElementById('iosClearAppBundleId')) {
                                    document.getElementById('iosClearAppBundleId').value = actionData.bundle_id || '';
                                }
                                break;
                        }
                    }, 50); // Small delay to ensure the form fields are created
                }
                break;
        }

        // Scroll to action builder
        document.querySelector('.action-builder-title').scrollIntoView({ behavior: 'smooth' });

        // Update add action button to show we're editing
        const addActionBtn = document.getElementById('addAction');
        if (addActionBtn) {
            addActionBtn.textContent = 'Update Action';
            addActionBtn.classList.replace('btn-success', 'btn-warning');
            // Make sure we store the index as a string to avoid issues with dataset
            addActionBtn.dataset.editingIndex = String(index);
            console.log(`Set editing index to: ${index} (${typeof index}) - stored as: ${addActionBtn.dataset.editingIndex}`);

            // Add cancel button if not already present
            if (!document.getElementById('cancelEditBtn')) {
                const cancelBtn = document.createElement('button');
                cancelBtn.id = 'cancelEditBtn';
                cancelBtn.className = 'btn btn-outline-secondary ms-2';
                cancelBtn.textContent = 'Cancel';
                cancelBtn.addEventListener('click', () => this.cancelEdit());
                addActionBtn.parentNode.appendChild(cancelBtn);
            }
        }
    }

    cancelEdit() {
        // Reset the add action button
        const addActionBtn = document.getElementById('addAction');
        if (addActionBtn) {
            addActionBtn.textContent = 'Add Action';
            addActionBtn.classList.replace('btn-warning', 'btn-success');
            delete addActionBtn.dataset.editingIndex;
        }

        // Remove cancel button
        const cancelBtn = document.getElementById('cancelEditBtn');
        if (cancelBtn) {
            cancelBtn.remove();
        }

        // Reset all form fields
        this.resetActionForm();
    }

    // Method to duplicate an action
    duplicateAction(index) {
        // Validate index
        if (index < 0 || index >= this.currentActions.length) {
            console.error(`Invalid index for duplication: ${index}`);
            this.logAction('error', `Invalid action index for duplication: ${index}`);
            return;
        }

        // Create a deep copy of the action
        const originalAction = this.currentActions[index];
        const duplicatedAction = JSON.parse(JSON.stringify(originalAction));

        // Add a timestamp to make it unique
        duplicatedAction.timestamp = Date.now();

        // Insert the duplicated action after the original
        this.currentActions.splice(index + 1, 0, duplicatedAction);

        // Add the duplicated action to the UI
        if (this.actionManager) {
            // Add the action to the UI at the specific position
            this.actionManager.addActionToListAtPosition(duplicatedAction, index + 1);

            // Update step numbers
                            this.updateStepNumbers();

                            // Log the action
            this.logAction('success', `Duplicated action at index ${index + 1}`);
        } else {
            this.logAction('error', 'Action manager not available for duplication');
        }
    }



    // Refresh screenshot from a URL
    refreshScreenshotFromUrl(url) {
        if (!url) return;
        console.log(`[refreshScreenshotFromUrl] Received URL: ${url}`); // Log received URL

        // Ensure screenshots are loaded from /static/screenshots/ instead of /screenshots/
        let modifiedUrl = url;
        if (modifiedUrl.startsWith('/screenshots/')) {
            modifiedUrl = '/static' + modifiedUrl;
        }

        // Add timestamp to prevent caching
        const timestamp = new Date().getTime();

        // Check if the URL already has a query parameter
        let screenshotUrl;
        if (modifiedUrl.includes('?')) {
            // URL already has query params, append with '&'
            screenshotUrl = `${modifiedUrl}&t=${timestamp}`;
        } else {
            // No query params, append with '?'
            screenshotUrl = `${modifiedUrl}?t=${timestamp}`;
        }

        console.log(`[refreshScreenshotFromUrl] Setting IMG SRC to: ${screenshotUrl}`); // Log final URL

        // Track previous error state for this image load
        if (!this._screenshotErrorCount) {
            this._screenshotErrorCount = 0;
        }

        // Update the image source
        if (this.deviceScreen) {
            // Add error handling for the image
            this.deviceScreen.onerror = async (error) => {
                console.error('Screenshot refresh failed:', error);
                this._screenshotErrorCount++;

                // Don't show too many errors to avoid spamming the user
                if (this._screenshotErrorCount <= 2) {
                    this.logAction('warning', 'Screenshot refresh failed, attempting recovery...');

                    // Attempt recovery via API endpoint
                    try {
                        // Call the recovery endpoint
                        const result = await this.fetchApi('device/reconnect', 'POST');
                        if (result.status === 'success') {
                            this.logAction('info', 'Device connection recovered. Refreshing...');
                            // Reset error count
                            this._screenshotErrorCount = 0;

                            // Wait briefly before retrying
                            setTimeout(() => {
                                this.refreshDevice();
                            }, 1000);
                        } else {
                            this.logAction('error', `Recovery failed: ${result.message}`);
                        }
                    } catch (recoveryError) {
                        console.error('Recovery attempt failed:', recoveryError);
                        this.logAction('error', 'Failed to recover device connection');
                    }
                }
            };

            // Reset error count when image loads successfully
            this.deviceScreen.onload = () => {
                this._screenshotErrorCount = 0;
            };

            this.deviceScreen.src = screenshotUrl;
        }
    }

    // Refresh the current device screenshot
    async refreshDevice() {
        if (!this.isConnected) {
            console.log('Cannot refresh device - not connected');
            return;
        }

        try {
            // Show loading indicator
            this.showLoading();

            // Take a new screenshot
            const result = await this.fetchApi('screenshot');

            // Hide loading indicator
            this.hideLoading();

            if (result.status === 'success') {
                // Refresh the screenshot
                this.refreshScreenshotFromUrl(result.screenshot_url);

                // Update device info if available
                if (result.device_info) {
                    this.updateDeviceInfo(result.device_info);
                }
            } else {
                // Log the error
                this.logAction('error', `Failed to refresh screenshot: ${result.error || 'Unknown error'}`);

                // If this was a server error, try recovery
                if (result.error && result.error.toLowerCase().includes('server')) {
                    this.logAction('info', 'Attempting automatic recovery...');
                    const recovery = await this.fetchApi('device/reconnect', 'POST');
                    if (recovery.status === 'success') {
                        this.logAction('success', 'Device connection recovered');
                        // Try again after a short delay
                        setTimeout(() => this.refreshDevice(), 1000);
                    }
                }
            }
        } catch (error) {
            this.hideLoading();
            this.logAction('error', `Error refreshing device: ${error.message}`);
            console.error('Error refreshing device:', error);
        }
    }

    // Update device info in the UI
    updateDeviceInfo(deviceInfo) {
        if (!deviceInfo) return;

        this.deviceInfo = deviceInfo;
        this.logAction('info', `Device info updated: ${deviceInfo.model || deviceInfo.device_name}`);

        // Check if this is an emulator and apply fixes if needed
        if (deviceInfo.is_emulator) {
            this.logAction('info', 'Emulator detected - starting maintenance routine');
            this.startEmulatorMaintenance();
        } else if (this.emulatorMaintenanceInterval) {
            // Stop maintenance if device is no longer an emulator
            clearInterval(this.emulatorMaintenanceInterval);
            this.emulatorMaintenanceInterval = null;
        }
    }

    startEmulatorMaintenance() {
        // Offer to fix common emulator issues (UiAutomator2 crashes, etc.)
        if (this.deviceInfo && this.deviceInfo.is_emulator) {
            if (confirm('This device appears to be an emulator. Would you like to apply optimizations and fixes for better stability?')) {
                this.applyEmulatorFixes();
            } else {
                this.logAction('info', 'Emulator optimizations skipped. You can apply them later if needed.');
            }
        } else {
            this.logAction('info', 'Physical device detected - no emulator-specific optimizations needed.');
        }
    }

    async applyEmulatorFixes() {
        // Apply various fixes for emulator stability
        try {
            this.logAction('info', 'Applying emulator stability fixes...');
            this.showLoading('Applying fixes...');

            const result = await this.fetchApi('device/fix_emulator', 'POST');

            this.hideLoading();

            if (result.status === 'success') {
                this.logAction('success', 'Emulator fixes applied successfully');
                this.showToast('Success', 'Emulator fixes applied successfully', 'success');

                // Refresh screenshot if provided
                if (result.screenshot_url) {
                    this.refreshScreenshotFromUrl(result.screenshot_url);
                }

                // Check if recovery was needed
                if (result.recovery_needed) {
                    this.logAction('warning', 'Device connection was reset during fixes. Some UI state might need refresh.');
                }

                return true;
            } else {
                this.logAction('error', result.message || 'Failed to apply emulator fixes');
                this.showToast('Error', result.message || 'Failed to apply emulator fixes', 'error');
                return false;
            }
        } catch (error) {
            this.hideLoading();
            this.logAction('error', `Error applying emulator fixes: ${error.message}`);
            this.showToast('Error', `Failed to apply fixes: ${error.message}`, 'error');
            return false;
        }
    }

    // Add a new method specifically for setting up listeners that depend on TestCaseManager
    setupTestCaseEventListeners() {
         if (!this.testCaseManager) {
            console.error("TestCaseManager not initialized. Cannot set up related event listeners.");
            return;
        }
        console.log("Setting up TestCaseManager dependent event listeners...");

        // Test Cases tab buttons
        if (this.refreshTestCasesBtn) {
             // Ensure only one listener is added
             this.refreshTestCasesBtn.removeEventListener('click', this.handleRefreshTestCases); // Remove previous if any
             this.handleRefreshTestCases = () => this.testCaseManager.loadAllTestCases(); // Store handler reference
            this.refreshTestCasesBtn.addEventListener('click', this.handleRefreshTestCases);
        }

        if (this.testCaseSearchInput) {
             this.testCaseSearchInput.removeEventListener('input', this.handleFilterTestCases);
             this.handleFilterTestCases = () => this.testCaseManager.filterTestCases();
            this.testCaseSearchInput.addEventListener('input', this.handleFilterTestCases);
        }

        if (this.clearTestCaseSearchBtn) {
            this.clearTestCaseSearchBtn.removeEventListener('click', this.handleClearTestCaseSearch);
            this.handleClearTestCaseSearch = () => {
                 if (this.testCaseSearchInput) this.testCaseSearchInput.value = '';
                this.testCaseManager.filterTestCases();
            };
            this.clearTestCaseSearchBtn.addEventListener('click', this.handleClearTestCaseSearch);
        }

        if (this.sortByNameBtn) {
             this.sortByNameBtn.removeEventListener('click', this.handleSortByName);
             this.handleSortByName = () => {
                 this.testCasesSort = 'name';
                 if (this.sortByNameBtn) this.sortByNameBtn.classList.add('active');
                 if (this.sortByDateBtn) this.sortByDateBtn.classList.remove('active');
                 this.testCaseManager.displayTestCases();
             };
            this.sortByNameBtn.addEventListener('click', this.handleSortByName);
        }

        if (this.sortByDateBtn) {
             this.sortByDateBtn.removeEventListener('click', this.handleSortByDate);
             this.handleSortByDate = () => {
                 this.testCasesSort = 'date';
                 if (this.sortByDateBtn) this.sortByDateBtn.classList.add('active');
                 if (this.sortByNameBtn) this.sortByNameBtn.classList.remove('active');
                 this.testCaseManager.displayTestCases();
             };
            this.sortByDateBtn.addEventListener('click', this.handleSortByDate);
        }

        // Setup test-cases-tab activation event listener here
        const testCasesTabBtn = document.getElementById('test-cases-tab-btn');
        if (testCasesTabBtn) {
             // Use shown.bs.tab to load *after* the tab is visible
             const tabEl = document.querySelector('#test-cases-tab-btn'); // Target the button correctly
             if (tabEl) {
                // Clean up potential old listeners (safer)
                const oldClickHandler = () => { /* no-op */ };
                tabEl.removeEventListener('click', oldClickHandler);
                tabEl.removeEventListener('shown.bs.tab', this.handleTestCasesTabShown);

                // Define the handler using the manager
                this.handleTestCasesTabShown = () => {
                   console.log("Test Cases Tab shown, loading test cases via manager...");
                    if (this.testCaseManager) {
                         this.testCaseManager.loadAllTestCases();
                     } else {
                         console.error("TestCaseManager not available when tab shown event fired.");
                     }
                };
                tabEl.addEventListener('shown.bs.tab', this.handleTestCasesTabShown);

                // Initial load if tab is already active when listener is attached
                if (tabEl.classList.contains('active') && this.testCaseManager) {
                    console.log("Test Cases Tab already active, loading initial cases...");
                    this.testCaseManager.loadAllTestCases();
                }
            }
        }
         console.log("TestCaseManager listeners setup complete.");
    }

    // Setup Save and Load buttons
    setupSaveLoadButtons() {
        // Get fresh references to the buttons
        const saveRecordingBtn = document.getElementById('saveRecordingBtn');
        const loadRecordingBtn = document.getElementById('loadRecordingBtn');

        console.log('Setting up Save/Load buttons:', {
            saveBtn: saveRecordingBtn,
            loadBtn: loadRecordingBtn
        });

        // Add event listener for Load Recording button
        if (loadRecordingBtn) {
            // Remove any existing click listeners to prevent duplicates
            loadRecordingBtn.replaceWith(loadRecordingBtn.cloneNode(true));

            // Get fresh reference after clone
            const freshLoadBtn = document.getElementById('loadRecordingBtn');

            // Add proper click event listener
            freshLoadBtn.addEventListener('click', () => {
                console.log('Load button clicked');
                this.loadRecording();
            });

            console.log('Load button event listener added');
        } else {
            console.error('Load button not found in DOM');
        }

        // We now use the data-bs-toggle attribute directly on the save button for opening the modal
        // Just set up the test case name input and confirm button handlers
        const testCaseNameInput = document.getElementById('testCaseName');
        const confirmSaveButton = document.getElementById('confirmSaveTestCase');
        const saveTestCaseModal = document.getElementById('saveTestCaseModal');

        if (testCaseNameInput && confirmSaveButton && saveTestCaseModal) {
            // Clear and focus on input when the modal is shown
            saveTestCaseModal.addEventListener('shown.bs.modal', () => {
                console.log('Save modal shown');
                testCaseNameInput.value = ''; // Clear the input field
                testCaseNameInput.focus();
            });

            // Add keyboard support for Enter key
            testCaseNameInput.addEventListener('keypress', (event) => {
                if (event.key === 'Enter') {
                    confirmSaveButton.click();
                }
            });

            // Set up the confirm save button functionality
            confirmSaveButton.onclick = async () => {
                console.log('Save button clicked');
                await this.saveTestCase(testCaseNameInput.value.trim());
            };
        } else {
            console.error('Save modal elements not found', {
                testCaseNameInput,
                confirmSaveButton,
                saveTestCaseModal
            });
        }
    }

    // Method to handle Save As
    async saveRecordingAs() {
        const saveTestCaseModal = document.getElementById('saveTestCaseModal');
        const testCaseNameInput = document.getElementById('testCaseName');
        const confirmSaveButton = document.getElementById('confirmSaveTestCase');

        if (!saveTestCaseModal || !testCaseNameInput || !confirmSaveButton) {
            this.logAction('error', 'Unable to open save dialog - UI components not found');
            return;
        }

        // Check if there are actions to save
        if (!this.currentActions || this.currentActions.length === 0) {
            this.logAction('error', 'No actions to save');
            this.showToast('Error', 'No actions to save', 'error', 3000);
            return;
        }

        // Initialize the modal - Use getOrCreateInstance for stability
        let modalInstance;
        try {
             modalInstance = bootstrap.Modal.getOrCreateInstance(saveTestCaseModal);
        } catch (e) {
             console.error("Error getting or creating Bootstrap modal instance for saveTestCaseModal:", e);
             this.logAction('error', 'Failed to initialize save dialog.');
             return;
        }

        // Clear and focus input when modal is shown
        saveTestCaseModal.addEventListener('shown.bs.modal', () => {
            testCaseNameInput.value = '';
            testCaseNameInput.focus();
        });

        // Handle modal hidden event to ensure the page remains interactive
        saveTestCaseModal.addEventListener('hidden.bs.modal', () => {
            console.log("Save modal hidden"); // Optional log
        }, { once: true }); // <--- Add this option

        // Set up the confirm save button functionality
        confirmSaveButton.onclick = async () => {
            const testCaseName = testCaseNameInput.value.trim();
            if (!testCaseName) {
                this.logAction('error', 'Please enter a test case name');
                return;
            }

            try {
                this.showLoading();

                const result = await this.fetchApi('recording/save', 'POST', {
                    name: testCaseName,
                    currentActions: this.currentActions,
                    isSaveAs: true
                });

                this.hideLoading();

                if (result.status === 'saved') {
                    this.logAction('success', `Test case saved as ${result.filename}`);
                    // Update current test case name for future saves
                    this.currentTestCaseName = testCaseName;
                    // Hide the modal
                    if (modalInstance) {
                        modalInstance.hide();
                    }
                } else {
                    this.logAction('error', result.error || 'Failed to save test case');
                }
            } catch (error) {
                this.hideLoading();
                this.logAction('error', `Save error: ${error.message}`);
                console.error('Save error:', error);
            }
        };

        // Add keyboard support for Enter key
        testCaseNameInput.addEventListener('keypress', (event) => {
            if (event.key === 'Enter') {
                confirmSaveButton.click();
            }
        });

        // Show the modal
        try {
             if (modalInstance) {
                 modalInstance.show();
             } else {
                  throw new Error("Modal instance could not be obtained.");
             }
        } catch (e) {
             console.error("Error showing saveTestCaseModal:", e);
             this.logAction('error', 'Failed to show save dialog.');
        }
    }

    // Settings related methods
    setupSettingsTab() {
        console.log('Setting up Settings tab');
        // Load the current settings when the tab is shown
        this.loadSettings();
    }

    async loadSettings() {
        try {
            console.log('Loading settings...');

            const response = await fetch('/api/settings', {
                method: 'GET',
                headers: { 'Content-Type': 'application/json' }
            });

            if (!response.ok) {
                throw new Error(`Error loading settings: ${response.status}`);
            }

            const settings = await response.json();
            console.log('Loaded settings:', settings);

            // Update the settings state
            this.settings = settings;

            // Update the UI with loaded values
            // Folder paths
            document.getElementById('testCasesDir').value = settings.testcases_dir || 'test_cases';
            document.getElementById('reportsDir').value = settings.reports_dir || 'reports';
            document.getElementById('referenceImagesDir').value = settings.reference_images_dir || 'reference_images';
            document.getElementById('filesToPushDir').value = settings.files_to_push_dir || 'files_to_push';

            // Global Values
            this.populateGlobalValuesTable(settings.global_values || {});
        } catch (error) {
            console.error('Error loading settings:', error);
            this.logAction('error', `Failed to load settings: ${error.message}`);
            // Only show toast on error
            this.showToast('Failed to load settings', 'error');
        }
    }

    // Populate the global values table
    populateGlobalValuesTable(globalValues) {
        const tbody = document.getElementById('globalValuesTableBody');
        tbody.innerHTML = ''; // Clear existing rows

        // Add rows for each parameter
        Object.entries(globalValues).forEach(([key, value]) => {
            this.addGlobalValueRow(key, value);
        });

        // Add event listener for the Add Parameter button if not already set
        const addBtn = document.getElementById('addGlobalParameterBtn');
        if (addBtn) {
            // Remove existing listener to prevent duplicates
            const newAddBtn = addBtn.cloneNode(true);
            addBtn.parentNode.replaceChild(newAddBtn, addBtn);

            // Add new listener
            newAddBtn.addEventListener('click', () => {
                this.addGlobalValueRow('', '');
            });
        }
    }

    // Add a row to the global values table
    addGlobalValueRow(name = '', value = '') {
        const tbody = document.getElementById('globalValuesTableBody');
        const row = document.createElement('tr');

        row.innerHTML = `
            <td>
                <input type="text" class="form-control param-name" value="${name}" placeholder="Parameter name">
            </td>
            <td>
                <input type="text" class="form-control param-value" value="${value}" placeholder="Parameter value">
            </td>
            <td class="text-center">
                <button type="button" class="btn btn-sm btn-outline-danger delete-param">
                    <i class="bi bi-trash"></i>
                </button>
                <button type="button" class="btn btn-sm btn-outline-primary add-param-below ms-1">
                    <i class="bi bi-plus"></i>
                </button>
            </td>
        `;

        // Add event listeners
        const deleteBtn = row.querySelector('.delete-param');
        if (deleteBtn) {
            deleteBtn.addEventListener('click', () => {
                row.remove();
            });
        }

        const addBelowBtn = row.querySelector('.add-param-below');
        if (addBelowBtn) {
            addBelowBtn.addEventListener('click', () => {
                this.addGlobalValueRow('', '');
            });
        }

        tbody.appendChild(row);
    }

    // Save settings
    async saveSettings() {
        try {
            // Get values from form inputs
            const settings = {
                testcases_dir: document.getElementById('testCasesDir').value || 'test_cases',
                reports_dir: document.getElementById('reportsDir').value || 'reports',
                reference_images_dir: document.getElementById('referenceImagesDir').value || 'reference_images',
                files_to_push_dir: document.getElementById('filesToPushDir').value || 'files_to_push',
                global_values: this.collectGlobalValues()
            };

            // First save directory paths
            const directoryPaths = {
                test_cases_dir: settings.testcases_dir,
                reports_dir: settings.reports_dir,
                reference_images_dir: settings.reference_images_dir,
                test_suites_dir: document.getElementById('testSuitesDir')?.value || 'test_suites',
                files_to_push_dir: settings.files_to_push_dir
            };

            console.log("Saving directory paths:", directoryPaths);

            // Save directory paths to server
            const dirResponse = await fetch('/api/directory_paths', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(directoryPaths)
            });

            if (!dirResponse.ok) {
                throw new Error(`Failed to save directory paths: ${dirResponse.status}`);
            }

            const dirResult = await dirResponse.json();
            console.log("Directory paths save result:", dirResult);

            if (!dirResult.success) {
                throw new Error("Failed to save directory paths: " + (dirResult.error || "Unknown error"));
            }

            // Now save the global values
            const response = await fetch('/api/settings', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settings)
            });

            if (!response.ok) {
                throw new Error(`Failed to save settings: ${response.status}`);
            }

            this.showToast('Settings saved successfully', 'success');
            console.log('Settings saved successfully');

            // Reload test cases to reflect new directory
            // Check if loadTestCases is available globally or as a method
            if (typeof loadTestCases === 'function') {
                // Call the global function
                loadTestCases();
                // Store a reference for future use
                this.loadTestCases = loadTestCases;
            } else if (typeof this.loadTestCases === 'function') {
                // Call the method if it exists
                this.loadTestCases();
            } else if (typeof window.loadTestCases === 'function') {
                // Try window.loadTestCases as a last resort
                window.loadTestCases();
                // Store a reference for future use
                this.loadTestCases = window.loadTestCases;
            } else {
                console.log('No loadTestCases function available, skipping test cases reload');
                // Try to reload the page as a fallback
                setTimeout(() => {
                    window.location.reload();
                }, 1000);
            }

            // Reports tab references removed as it's no longer needed
        } catch (error) {
            console.error('Error saving settings:', error);
            this.showToast(`Failed to save settings: ${error.message}`, 'error');
        }
    }

    // Collect global values from the table
    collectGlobalValues() {
        const globalValues = {};
        const rows = document.querySelectorAll('#globalValuesTableBody tr');

        rows.forEach(row => {
            const nameInput = row.querySelector('.param-name');
            const valueInput = row.querySelector('.param-value');

            if (nameInput && valueInput && nameInput.value.trim()) {
                let value = valueInput.value.trim();

                // Try to convert to number if possible
                if (!isNaN(value) && value !== '') {
                    if (value.includes('.')) {
                        value = parseFloat(value);
                    } else {
                        value = parseInt(value);
                    }
                } else if (value.toLowerCase() === 'true') {
                    value = true;
                } else if (value.toLowerCase() === 'false') {
                    value = false;
                }

                globalValues[nameInput.value.trim()] = value;
            }
        });

        return globalValues;
    }

    // Reset settings to defaults
    resetSettings() {
        // Reset folder paths to defaults
        document.getElementById('testCasesDir').value = 'test_cases';
        document.getElementById('reportsDir').value = 'reports';
        document.getElementById('referenceImagesDir').value = 'reference_images';
        document.getElementById('filesToPushDir').value = 'files_to_push';

        // Clear global values table
        const tbody = document.getElementById('globalValuesTable').querySelector('tbody');
        tbody.innerHTML = '';
        this.addGlobalValueRow('', ''); // Add one empty row

        console.log('Settings reset to defaults');
        this.logAction('info', 'Settings reset to defaults');
        // Remove toast
        // this.showToast('Settings reset to defaults', 'info');
    }

    // Set up settings tab
    setupSettingsTab() {
        console.log('Setting up Settings tab');

        // Initialize UI elements
        this.initSettingsBrowseButtons();

        // Load the current settings when the tab is shown
        this.loadSettings();

        // Set up event listeners for settings buttons
        this.setupSettingsEventListeners();
    }

    // Initialize browse buttons for folder paths
    initSettingsBrowseButtons() {
        // This is a mock implementation since browser-based file system access is limited
        // In a real implementation, you would use a server-side directory picker
        const browseBtns = [
            'browseTestCasesBtn',
            'browseReportsBtn',
            'browseRefImagesBtn',
            'browseFilesToPushBtn'
        ];

        browseBtns.forEach(btnId => {
            const btn = document.getElementById(btnId);
            if (btn) {
                btn.addEventListener('click', () => {
                    this.showToast('Directory browsing is simulated in this web interface', 'info');
                    // For a real implementation, this would trigger a server-side directory browser
                });
            }
        });
    }

    // Set up event listeners for settings buttons
    setupSettingsEventListeners() {
        // Save button
        const saveBtn = document.getElementById('saveSettingsBtn');
        if (saveBtn) {
            // Remove existing listener to prevent duplicates
            const newSaveBtn = saveBtn.cloneNode(true);
            saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);

            // Add new listener
            newSaveBtn.addEventListener('click', () => {
                // Check if the global loadTestCases function exists
                if (typeof window.loadTestCases === 'function') {
                    // Store a reference to it in this object
                    this.loadTestCases = window.loadTestCases;
                }
                this.saveSettings();
            });
        }

        // Reset button
        const resetBtn = document.getElementById('resetSettingsBtn');
        if (resetBtn) {
            // Remove existing listener to prevent duplicates
            const newResetBtn = resetBtn.cloneNode(true);
            resetBtn.parentNode.replaceChild(newResetBtn, resetBtn);

            // Add new listener
            newResetBtn.addEventListener('click', () => {
                if (confirm('Are you sure you want to reset all settings to defaults?')) {
                    this.resetSettings();
                }
            });
        }

        // Add parameter button
        const addBtn = document.getElementById('addGlobalParameterBtn');
        if (addBtn) {
            // Remove existing listener to prevent duplicates
            const newAddBtn = addBtn.cloneNode(true);
            addBtn.parentNode.replaceChild(newAddBtn, addBtn);

            // Add new listener
            newAddBtn.addEventListener('click', () => {
                this.addGlobalValueRow('', '');
            });
        }

        // Save global parameters button
        const saveGlobalBtn = document.getElementById('saveGlobalParamsBtn');
        if (saveGlobalBtn) {
            // Remove existing listener to prevent duplicates
            const newSaveGlobalBtn = saveGlobalBtn.cloneNode(true);
            saveGlobalBtn.parentNode.replaceChild(newSaveGlobalBtn, saveGlobalBtn);

            // Add new listener
            newSaveGlobalBtn.addEventListener('click', () => {
                this.saveGlobalValues();
            });
        }

        // Refresh global parameters button
        const refreshBtn = document.getElementById('refreshGlobalParamsBtn');
        if (refreshBtn) {
            // Remove existing listener to prevent duplicates
            const newRefreshBtn = refreshBtn.cloneNode(true);
            refreshBtn.parentNode.replaceChild(newRefreshBtn, refreshBtn);

            // Add new listener
            newRefreshBtn.addEventListener('click', () => {
                this.loadSettings();
                this.showToast('Refreshed global parameters', 'success');
            });
        }
    }

    // Method to save only global values
    async saveGlobalValues() {
        try {
            // Show loading toast
            this.showToast('Saving global values...', 'info');

            // Collect only the global values
            const settings = {
                global_values: this.collectGlobalValues()
            };

            console.log('Saving global values:', settings.global_values);

            // Send global values to server
            const response = await fetch('/api/settings', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(settings)
            });

            if (!response.ok) {
                throw new Error(`Error saving global values: ${response.status}`);
            }

            const result = await response.json();

            if (result.status === 'success' || result.status === 'partial') {
                this.showToast('Global values saved successfully', 'success');
                this.logAction('success', 'Global values saved successfully');

                // If partial success, show warning
                if (result.status === 'partial') {
                    this.logAction('warning', `Warning: ${result.message}`);
                }
            } else {
                throw new Error(result.error || 'Unknown error');
            }
        } catch (error) {
            console.error('Error saving global values:', error);
            this.logAction('error', `Failed to save global values: ${error.message}`);
            this.showToast('Failed to save global values', 'error');
        }
    }

    escapedXml(xml) {
        if (!xml) return '';

        return xml
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&apos;');
    }

    escapeHtml(text) {
        if (text === null || text === undefined) return '';

        if (typeof text !== 'string') {
            text = String(text);
        }

        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#039;');
    }

    async loadSessionInfo() {
        // This method is used by the element inspector to fetch session info
        try {
            // Use the correct endpoint that returns fuller session information
            const data = await this.fetchApi('session/info', 'GET');

            if (data.success) {
                return data.session_info;
            } else {
                this.logAction('error', 'Failed to load session info: ' + (data.error || 'Unknown error'));
                return null;
            }
        } catch (error) {
            this.logAction('error', `Error loading session info: ${error.message}`);
            return null;
        }
    }

    async executeSingleAction() {
        // This method was referenced in event listeners but never implemented
        // Adding a basic implementation to execute the currently configured action

        try {
            this.logAction('info', 'Executing single action...');

            // Get action data from form
            const actionType = this.actionTypeSelect.value;

            if (!actionType) {
                this.logAction('error', 'No action type selected');
                return;
            }

            // Create a basic action object from the form
            const actionData = { type: actionType };

            // Add parameters based on action type
            switch (actionType) {
                case 'tap':
                    // Check if we have fallback locators but no primary locator
                    if ((!actionData.locator_type || !actionData.locator_value) &&
                        actionData.fallback_locators && actionData.fallback_locators.length > 0) {
                        // Switch directly to fallback tab
                        document.getElementById('tap-fallback-tab').click();

                        // Load fallback locators
                        if (this.fallbackLocatorsManager) {
                            this.fallbackLocatorsManager.loadFallbackLocators('tap', actionData);
                        }

                        // Set timeout and interval
                        document.getElementById('tapLocatorTimeout').value = actionData.timeout || 10;
                        document.getElementById('tapLocatorInterval').value = actionData.interval || 0.5;
                    }
                    // Determine which method to use based on action data
                    else if (actionData.method === 'locator' || (actionData.locator_type && actionData.locator_value)) {
                        // Switch to locator tab
                        document.getElementById('tap-locator-tab').click();

                        // Fill locator fields
                        document.getElementById('tapLocatorType').value = actionData.locator_type || 'id';
                        document.getElementById('tapLocatorValue').value = actionData.locator_value || '';
                        document.getElementById('tapLocatorTimeout').value = actionData.timeout || 10;
                        document.getElementById('tapLocatorInterval').value = actionData.interval || 0.5;

                        // Load fallback locators if available
                        if (this.fallbackLocatorsManager && actionData.fallback_locators && actionData.fallback_locators.length > 0) {
                            // Switch to fallback tab if we have fallback locators
                            document.getElementById('tap-fallback-tab').click();
                            this.fallbackLocatorsManager.loadFallbackLocators('tap', actionData);
                        }
                    }
                    else if (actionData.method === 'image' || actionData.image_filename) {
                        // Switch to image tab
                        document.getElementById('tap-image-tab').click();

                        // Fill image fields
                        document.getElementById('tapImageFilename').value = actionData.image_filename || '';
                        document.getElementById('tapThreshold').value = actionData.threshold || 0.7;
                        document.getElementById('tapTimeout').value = actionData.timeout || 20;
                    }
                    else {
                        // Switch to coordinates tab
                        document.getElementById('tap-coordinates-tab').click();

                        // Fill coordinate fields
                        document.getElementById('tapX').value = actionData.x || 0;
                        document.getElementById('tapY').value = actionData.y || 0;
                    }
                    break;

                case 'doubleTap':
                    // Check if we have fallback locators but no primary locator
                    if ((!actionData.locator_type || !actionData.locator_value) &&
                        actionData.fallback_locators && actionData.fallback_locators.length > 0) {
                        // Switch directly to fallback tab
                        document.getElementById('doubletap-fallback-tab').click();

                        // Load fallback locators
                        if (this.fallbackLocatorsManager) {
                            this.fallbackLocatorsManager.loadFallbackLocators('doubleTap', actionData);
                        }

                        // Set timeout and interval
                        document.getElementById('doubleTapLocatorTimeout').value = actionData.timeout || 10;
                        document.getElementById('doubleTapLocatorInterval').value = actionData.interval || 0.5;
                    }
                    // Determine which method to use based on action data
                    else if (actionData.method === 'locator' || (actionData.locator_type && actionData.locator_value)) {
                        // Switch to locator tab
                        document.getElementById('doubletap-locator-tab').click();

                        // Fill locator fields
                        document.getElementById('doubleTapLocatorType').value = actionData.locator_type || 'id';
                        document.getElementById('doubleTapLocatorValue').value = actionData.locator_value || '';
                        document.getElementById('doubleTapLocatorTimeout').value = actionData.timeout || 10;
                        document.getElementById('doubleTapLocatorInterval').value = actionData.interval || 0.5;

                        // Load fallback locators if available
                        if (this.fallbackLocatorsManager && actionData.fallback_locators && actionData.fallback_locators.length > 0) {
                            // Switch to fallback tab if we have fallback locators
                            document.getElementById('doubletap-fallback-tab').click();
                            this.fallbackLocatorsManager.loadFallbackLocators('doubleTap', actionData);
                        }
                    }
                    else if (actionData.method === 'image' || actionData.image_filename) {
                        // Switch to image tab
                        document.getElementById('doubletap-image-tab').click();

                        const imageFilename = actionData.image_filename || '';
                        
                        // Check if it's a manually entered path (containing env[] or not in dropdown)
                        const selectElement = document.getElementById('doubleTapImageFilename');
                        let foundInDropdown = false;
                        
                        if (selectElement) {
                            // Try to find in dropdown
                            for (let i = 0; i < selectElement.options.length; i++) {
                                if (selectElement.options[i].value === imageFilename) {
                                    selectElement.selectedIndex = i;
                                    foundInDropdown = true;
                                    break;
                                }
                            }
                        }
                        
                        // If not found in dropdown or starts with env[], use text input
                        if (!foundInDropdown || imageFilename.startsWith('env[')) {
                            document.getElementById('doubleTapImageUseText').checked = true;
                            document.getElementById('doubleTapImageTextInputGroup').style.display = 'flex';
                            document.getElementById('doubleTapImageTextInput').value = imageFilename;
                            document.getElementById('doubleTapImageFilename').disabled = true;
                        } else {
                            document.getElementById('doubleTapImageUseText').checked = false;
                            document.getElementById('doubleTapImageTextInputGroup').style.display = 'none';
                            document.getElementById('doubleTapImageFilename').disabled = false;
                            document.getElementById('doubleTapImageFilename').value = imageFilename;
                        }
                        
                        // Reset validation messages
                        document.getElementById('doubleTapImageValidationSuccess').style.display = 'none';
                        document.getElementById('doubleTapImageValidationError').style.display = 'none';
                        
                        document.getElementById('doubleTapThreshold').value = actionData.threshold || 0.7;
                        document.getElementById('doubleTapTimeout').value = actionData.timeout || 20;
                    }
                    else {
                        // Switch to coordinates tab
                        document.getElementById('doubletap-coordinates-tab').click();

                        // Fill coordinate fields
                        document.getElementById('doubleTapX').value = actionData.x || 0;
                        document.getElementById('doubleTapY').value = actionData.y || 0;
                    }
                    break;

                case 'swipe':
                    const startX = parseInt(document.getElementById('swipeStartX').value);
                    const startY = parseInt(document.getElementById('swipeStartY').value);
                    const endX = parseInt(document.getElementById('swipeEndX').value);
                    const endY = parseInt(document.getElementById('swipeEndY').value);
                    if (isNaN(startX) || isNaN(startY) || isNaN(endX) || isNaN(endY)) {
                        this.logAction('error', 'Invalid swipe coordinates');
                        return;
                    }
                    actionData.start_x = startX;
                    actionData.start_y = startY;
                    actionData.end_x = endX;
                    actionData.end_y = endY;
                    break;

                case 'text':
                    const text = document.getElementById('inputText').value;
                    if (!text) {
                        this.logAction('error', 'No text specified');
                        return;
                    }
                    actionData.text = text;
                    break;

                case 'key':
                    document.getElementById('keyCode').value = actionData.key_code;
                    break;

                case 'wait':
                    document.getElementById('waitTime').value = actionData.duration;
                    break;

                case 'launchApp':
                    document.getElementById('appPackage').value = actionData.package;
                    break;

                case 'terminateApp':
                    document.getElementById('terminatePackage').value = actionData.package;
                    break;

                case 'restartApp':
                    document.getElementById('restartPackage').value = actionData.package;
                    break;

                case 'uninstallApp':
                    document.getElementById('uninstallPackage').value = actionData.package_id;
                    break;

                case 'waitTill':
                    // Set locator type
                    if (actionData.locator_type) {
                        document.getElementById('waitTillLocatorType').value = actionData.locator_type;
                        // Trigger the change event handler to show/hide fields
                        this.handleWaitTillLocatorTypeChange();

                        if (actionData.locator_type === 'image') {
                            document.getElementById('waitTillImage').value = actionData.image;
                        } else {
                            document.getElementById('waitTillLocator').value = actionData.locator_value;
                        }
                    } else {
                        // Handle legacy data format (before the change)
                        document.getElementById('waitTillLocatorType').value = 'image';
                        this.handleWaitTillLocatorTypeChange();
                        document.getElementById('waitTillImage').value = actionData.image;
                    }

                    document.getElementById('waitTillTimeout').value = actionData.timeout;
                    document.getElementById('waitTillInterval').value = actionData.interval;
                    break;

                case 'exists':
                    document.getElementById('existsImage').value = actionData.image;
                    break;

                case 'clickElement':
                    document.getElementById('clickElementLocatorType').value = actionData.locator_type;
                    document.getElementById('clickElementLocator').value = actionData.locator_value;
                    document.getElementById('clickElementTimeout').value = actionData.timeout;
                    break;

                case 'doubleClick':
                    document.getElementById('doubleClickX').value = actionData.x;
                    document.getElementById('doubleClickY').value = actionData.y;
                    break;

                case 'textClear':
                    document.getElementById('textClearInput').value = actionData.text;
                    document.getElementById('textClearDelay').value = actionData.delay;
                    break;
                case 'clickImage': // <-- Add case for clickImage
                    document.getElementById('ocrTextToFind').value = actionData.text_to_find || '';
                    document.getElementById('ocrTimeout').value = actionData.timeout || 20;
                    break;
                case 'clickImageAirtest': // Added case for Airtest
                    document.getElementById('airtestImageFilename').value = actionData.image_filename || '';
                    document.getElementById('airtestThreshold').value = actionData.threshold || 0.7;
                    document.getElementById('airtestTimeout').value = actionData.timeout || 20;
                    break;
                case 'waitImageAirtest': // Added case
                    document.getElementById('waitAirtestImageFilename').value = actionData.image_filename || '';
                    document.getElementById('waitAirtestThreshold').value = actionData.threshold || 0.7;
                    document.getElementById('waitAirtestTimeout').value = actionData.timeout || 30;
                    break;
                case 'doubleClickImageAirtest': // Added case
                    document.getElementById('doubleClickAirtestImageFilename').value = actionData.image_filename || '';
                    document.getElementById('doubleClickAirtestThreshold').value = actionData.threshold || 0.7;
                    document.getElementById('doubleClickAirtestTimeout').value = actionData.timeout || 20;
                    break;
                case 'hideKeyboard':
                    // Add any additional logic you want to execute when hideKeyboard is selected
                    console.log('Hide keyboard action selected');
                    break;
                case 'tapOnText':
                    // Set the text to find and other parameters
                    document.getElementById('tapOnTextToFind').value = actionData.text_to_find || '';
                    document.getElementById('tapOnTextTimeout').value = actionData.timeout || 30;
                    document.getElementById('tapOnTextDoubleTap').checked = actionData.double_tap === true;
                    break;
                case 'iosFunctions':
                    // Set the iOS function in the dropdown
                    const iosFunctionSelect = document.getElementById('iosFunction');
                    if (iosFunctionSelect && actionData.function_name) {
                        iosFunctionSelect.value = actionData.function_name;
                        
                        // Trigger the change handler to update the UI for this function
                        const changeEvent = new Event('change');
                        iosFunctionSelect.dispatchEvent(changeEvent);
                        
                        // Now fill in the function-specific parameters based on what fields were created by the change handler
                        setTimeout(() => {
                            switch (actionData.function_name) {
                                case 'press':
                                    if (document.getElementById('iosKeyName')) {
                                        document.getElementById('iosKeyName').value = actionData.key || '';
                                    } else if (document.getElementById('iosPressKey')) {
                                        document.getElementById('iosPressKey').value = actionData.key || '';
                                    }
                                    break;
                                case 'alert_click':
                                    if (document.getElementById('iosAlertButton')) {
                                        document.getElementById('iosAlertButton').value = actionData.button_text || '';
                                    } else if (document.getElementById('iosAlertButtonText')) {
                                        document.getElementById('iosAlertButtonText').value = actionData.button_text || '';
                                    }
                                    break;
                                case 'alert_wait':
                                    if (document.getElementById('iosAlertTimeout')) {
                                        document.getElementById('iosAlertTimeout').value = actionData.timeout || 2;
                                    }
                                    break;
                                case 'set_clipboard':
                                    if (document.getElementById('clipboardContent')) {
                                        document.getElementById('clipboardContent').value = actionData.content || '';
                                    } else if (document.getElementById('iosClipboardContent')) {
                                        document.getElementById('iosClipboardContent').value = actionData.content || '';
                                    }
                                    break;
                                case 'text':
                                    if (document.getElementById('iosTextInput')) {
                                        document.getElementById('iosTextInput').value = actionData.text || '';
                                        if (document.getElementById('iosTextEnter')) {
                                            document.getElementById('iosTextEnter').checked = actionData.enter !== false; // Default to true
                                        }
                                    } else if (document.getElementById('iosTextContent')) {
                                        document.getElementById('iosTextContent').value = actionData.text || '';
                                        if (document.getElementById('iosTextEnter')) {
                                            document.getElementById('iosTextEnter').checked = actionData.enter !== false;
                                        }
                                    }
                                    break;
                                case 'push':
                                    if (document.getElementById('iosPushSourcePath')) {
                                        document.getElementById('iosPushSourcePath').value = actionData.local_path || '';
                                    } else if (document.getElementById('iosPushLocalPath')) {
                                        document.getElementById('iosPushLocalPath').value = actionData.local_path || '';
                                    }
                                    
                                    if (document.getElementById('iosPushDestinationPath')) {
                                        document.getElementById('iosPushDestinationPath').value = actionData.remote_path || '';
                                    } else if (document.getElementById('iosPushRemotePath')) {
                                        document.getElementById('iosPushRemotePath').value = actionData.remote_path || '';
                                    }
                                    break;
                                case 'clear_app':
                                    if (document.getElementById('iosClearAppBundleID')) {
                                        document.getElementById('iosClearAppBundleID').value = actionData.bundle_id || '';
                                    } else if (document.getElementById('iosClearAppBundleId')) {
                                        document.getElementById('iosClearAppBundleId').value = actionData.bundle_id || '';
                                    }
                                    break;
                            }
                        }, 50); // Small delay to ensure the form fields are created
                    }
                    break;
                case 'addLog':
                    // Set the log message and screenshot flag
                    document.getElementById('addLogMessage').value = actionData.message || '';
                    document.getElementById('addLogTakeScreenshot').checked = actionData.take_screenshot !== false; // Default to true
                    break;
            }

            // Use the executeAction method to run the action
            const result = await window.executionManager.executeAction(actionData);

            if (result && result.success) {
                this.logAction('success', `Action executed: ${actionType}`);

                // Check if we have a screenshot URL in the response
                if (result.screenshot_url) {
                    console.log(`Received screenshot URL from server: ${result.screenshot_url}`);
                    this.refreshScreenshotFromUrl(result.screenshot_url);
                    this.logAction('info', `Screenshot refreshed from server URL: ${result.screenshot_url}`);
                } else {
                    console.log('No screenshot URL in response, using standard refresh');
                    // Fallback to standard refresh
                    this.refreshScreenshot();
                }
            } else {
                this.logAction('error', (result?.error || 'Failed to execute action'));

                // Even on error, try to refresh the screenshot if URL is provided
                if (result && result.screenshot_url) {
                    console.log(`Received screenshot URL from error response: ${result.screenshot_url}`);
                    this.refreshScreenshotFromUrl(result.screenshot_url);
                    this.logAction('info', 'Screenshot refreshed despite action error');
                }
            }
        } catch (error) {
            this.logAction('error', `Error executing action: ${error.message}`);
        }
    }

    // Re-index all actions across all loaded test cases after a deletion
    reIndexAllActions() {
        console.log("Re-indexing global action indices...");
        let globalIndex = 0;
        const allActionItems = document.querySelectorAll('#actionsList .action-item'); // Get all action items
        allActionItems.forEach(item => {
            item.dataset.actionIndex = globalIndex.toString();
            globalIndex++;
        });
        console.log(`Re-indexing complete. Total actions: ${globalIndex}`);
    }

    // Method to open the Appium Web Inspector in a new window
    async openWebInspector() {
        if (!this.isConnected) {
            this.logAction('error', 'Cannot open Web Inspector - no device connected');
            return;
        }

        this.logAction('info', 'Checking Appium Web Inspector availability...');
        this.showLoading('Checking inspector availability...');

        try {
            // Use our server-side API to check if the inspector is available
            // This avoids CORS issues
            const response = await fetch('/api/check_inspector');
            const data = await response.json();

            this.hideLoading();

            if (data.available) {
                // Inspector is available, open it in a new window
                this.logAction('info', 'Opening Appium Web Inspector in a new window...');

                // Get the base inspector URL
                const inspectorUrl = data.url || 'http://localhost:4723/inspector';

                // Manually construct query parameters, trying 127.0.0.1 and unencoded path
                const host = '127.0.0.1'; // Use 127.0.0.1 as inspector seems to default to it
                const port = data.appium_port || '4723';
                const path = '/wd/hub/'; // Try sending unencoded path
                const finalInspectorUrl = `${inspectorUrl}?host=${host}&port=${port}&path=${path}`;

                // Log session ID if available, for information
                if (data.session_info && data.session_info.session_id) {
                    const sessionId = data.session_info.session_id;
                    this.logAction('info', `Found active session ID: ${sessionId}. Inspector should list this session under 'Attach to Session'.`);
                } else {
                    this.logAction('warning', 'No active session ID found from API. Inspector connection details will be pre-filled, but session may not appear immediately.');
                }

                // Open the URL with pre-filled parameters
                this.logAction('info', `Opening URL: ${finalInspectorUrl}`);
                const newWindow = window.open(finalInspectorUrl, '_blank');

                if (newWindow) {
                    this.logAction('success', 'Opened Appium Web Inspector with pre-filled connection details.');
                    this.showToast('Opened Appium Inspector. Session should be listed under \'Attach to Session\'.', 'success', 4000);
                } else {
                    // If window.open returns null, it might be blocked by a popup blocker
                    this.logAction('warning', 'Popup blocker may have prevented opening the Appium Inspector');
                    this.showToast('Please allow popups for this site to use the Web Inspector', 'warning', 5000);
                }
            } else {
                // Inspector is not available
                const errorMessage = 'Appium Inspector plugin is not available. Please install it using: appium plugin install --source=npm appium-inspector-plugin';
                this.logAction('error', errorMessage);
                this.showToast('Appium Inspector plugin is not available. See logs for installation instructions.', 'error', 5000);

                // Ask user if they want to install the plugin
                if (confirm('The Appium Inspector plugin is not available. Would you like to see installation instructions?')) {
                    // Show installation instructions
                    const instructions = `
                    To install the Appium Inspector plugin, run the following commands in your terminal:

                    1. Install the plugin:
                       appium plugin install --source=npm appium-inspector-plugin

                    2. Restart Appium with the plugin enabled:
                       appium --use-plugins inspector

                    Or simply run the provided installation script:
                       ./install_appium_inspector_plugin.sh
                    `;

                    alert(instructions);
                }
            }
        } catch (error) {
            this.hideLoading();
            this.logAction('error', `Error checking Web Inspector availability: ${error.message}`);
            this.showToast('Error checking Web Inspector availability. See logs for details.', 'error', 5000);
        }
    }

    // Add method to initialize file upload handlers
    initFileUploadHandlers() {
        const uploadBtn = document.getElementById('uploadMediaBtn');
        const mediaFileInput = document.getElementById('mediaFile');
        const uploadStatus = document.getElementById('uploadStatus');
        const uploadProgressBar = document.getElementById('uploadProgressBar');
        const uploadMessage = document.getElementById('uploadMessage');

        if (uploadBtn && !uploadBtn._hasInitialized) {
            uploadBtn._hasInitialized = true;

            uploadBtn.addEventListener('click', () => {
                if (!mediaFileInput.files || mediaFileInput.files.length === 0) {
                    this.logAction('error', 'Please select a file first');
                    return;
                }

                const file = mediaFileInput.files[0];
                const formData = new FormData();
                formData.append('file', file);

                // Show upload status
                uploadStatus.classList.remove('d-none');
                uploadProgressBar.style.width = '0%';
                uploadMessage.textContent = 'Uploading...';

                // Create and configure XHR
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/api/upload_media', true);

                // Track upload progress
                xhr.upload.onprogress = (e) => {
                    if (e.lengthComputable) {
                        const percent = Math.round((e.loaded / e.total) * 100);
                        uploadProgressBar.style.width = `${percent}%`;
                        uploadMessage.textContent = `Uploading... ${percent}%`;
                    }
                };

                // Handle response
                xhr.onload = () => {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            if (response.status === 'success') {
                                uploadMessage.textContent = `File uploaded successfully: ${response.filename}`;
                                uploadProgressBar.className = 'progress-bar bg-success';

                                // Store the filename in a hidden field or data attribute
                                const mediaDestination = document.getElementById('mediaDestination');
                                if (!mediaDestination.value) {
                                    // If no custom destination was specified, store the default one
                                    if (this.platform && this.platform.toLowerCase() === 'android') {
                                        mediaDestination.value = `/sdcard/Pictures/${response.filename}`;
                                    } else if (this.platform && this.platform.toLowerCase() === 'ios') {
                                        mediaDestination.value = 'photos';
                                    }
                                }

                                this.logAction('success', `File uploaded: ${response.filename}`);
                            } else {
                                uploadMessage.textContent = `Upload failed: ${response.message || 'Unknown error'}`;
                                uploadProgressBar.className = 'progress-bar bg-danger';
                                this.logAction('error', `Upload failed: ${response.message || 'Unknown error'}`);
                            }
                        } catch (error) {
                            uploadMessage.textContent = 'Error processing server response';
                            uploadProgressBar.className = 'progress-bar bg-danger';
                            this.logAction('error', `Error processing server response: ${error.message}`);
                        }
                    } else {
                        uploadMessage.textContent = `Upload failed with status ${xhr.status}`;
                        uploadProgressBar.className = 'progress-bar bg-danger';
                        this.logAction('error', `Upload failed with status ${xhr.status}`);
                    }
                };

                // Handle network errors
                xhr.onerror = () => {
                    uploadMessage.textContent = 'Network error during upload';
                    uploadProgressBar.className = 'progress-bar bg-danger';
                    this.logAction('error', 'Network error during upload');
                };

                // Send the form data
                xhr.send(formData);
            });
        }
    }

    async executeAllActions() {
        try {
            // Update UI
            this.updateUIForExecuting(true);

            // Delete all screenshots before starting execution
            await this.deleteAllScreenshots();

            // Get all actions
            const actions = Array.from(document.querySelectorAll('#actionsList .action-item'))
                .map(actionItem => {
                    const actionData = JSON.parse(actionItem.dataset.actionData || '{}');
                    actionData.index = parseInt(actionItem.dataset.index || '0');
                    return actionData;
                });

            if (actions.length === 0) {
                this.logAction('warning', 'No actions to execute');
                this.updateUIForExecuting(false);
                return;
            }

            this.logAction('info', `ExecutionManager: Starting execution of ${actions.length} actions...`);

            // Sort actions by index
            actions.sort((a, b) => a.index - b.index);

            // Execute each action in sequence
            for (let i = 0; i < actions.length; i++) {
                const action = actions[i];

                // Skip if disabled
                if (action.disabled) {
                    this.logAction('info', `Skipping disabled action ${i + 1}/${actions.length}: ${this.actionManager.getActionDescription(action)}`);
                    continue;
                }

                this.logAction('info', `Executing action ${i + 1}/${actions.length}: ${this.actionManager.getActionDescription(action)}`);

                // Update UI for current action
                this.updateUIForExecuting(true, i);

                // Execute the action
                try {
                    await this.executeAction(action);
                } catch (actionError) {
                    this.logAction('error', `Action ${i + 1} failed: ${actionError.message}`);

                    // If set to continue on error, log and continue
                    const continueOnError = document.getElementById('continueOnError')?.checked || false;
                    if (!continueOnError) {
                        throw actionError; // Re-throw to stop execution
                    }
                }

                // Check if execution should stop
                if (this.stopRequested) {
                    this.logAction('warning', 'Execution stopped by user');
                    break;
                }

                // Get delay value (in ms)
                const delay = parseInt(document.getElementById('actionDelay')?.value || '0');
                if (delay > 0 && i < actions.length - 1) { // No delay after last action
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }

            if (!this.stopRequested) {
                this.logAction('success', 'Execution completed successfully.');

                // Check if we should get the latest report
                this.checkForLatestReport();
            }
        } catch (error) {
            this.logAction('error', `Execution failed: ${error.message}`);
        } finally {
            this.stopRequested = false;
            this.updateUIForExecuting(false);
        }
    }

    async checkForLatestReport() {
        try {
            // Wait a moment to ensure report is generated
            await new Promise(resolve => setTimeout(resolve, 1000));

            const response = await fetch('/api/reports/latest');
            const data = await response.json();

            if (data.status === 'success' && data.report_url) {
                // Show a floating action button to view the report
                // this.showReportButton(data.report_url); // Now handled by reportAndFormUtils
                if (this.reportAndFormUtils) {
                    this.reportAndFormUtils.showReportButton(data.report_url);
                }
            }
        } catch (error) {
            console.error('Error checking for report:', error);
        }
    }

    showValidationMessage(elementId, message, isError = false) {
        const element = document.getElementById(elementId);
        if (element) {
            if (elementId.includes('Error')) {
                // For error messages with specific message spans
                const messageSpan = element.querySelector('span');
                if (messageSpan) {
                    messageSpan.textContent = message;
                }
            }
            element.style.display = 'block';
            
            // Hide the opposite message type
            const oppositeType = elementId.includes('Error') ? elementId.replace('Error', 'Success') : elementId.replace('Success', 'Error');
            const oppositeElement = document.getElementById(oppositeType);
            if (oppositeElement) {
                oppositeElement.style.display = 'none';
            }
        }
    }

} 